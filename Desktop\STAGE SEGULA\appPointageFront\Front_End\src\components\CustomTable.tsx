'use client';

import { <PERSON>Circle, Hourglass, XCircle, <PERSON>ert<PERSON>riangle, CircleAlert } from 'lucide-react';
import React, { useState, useEffect, useMemo } from 'react';
import {
  ColumnDef,
  useReactTable,
  getCoreRowModel,
  getSortedRowModel,
  getFilteredRowModel,
  flexRender,
} from '@tanstack/react-table';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/src/components/ui/table';
import { Button } from './ui/button';
import { toast } from '@/src/components/ui/use-toast';
import { Dialog, DialogContent, DialogTitle, DialogOverlay, DialogPortal, DialogClose } from './ui/dialog';
import StatusBadge from "./ui/StatusBadge";

interface Utilisateur {
  id: number;
  matriculeSegula: string;
  nom: string;
  prenom: string;
  soldeConge: number;
}

interface RowData {
  id: string;
  utilisateur?: Utilisateur;
  statut?: string;
  raison_annulation?: string;
}

export interface DynamicTableProps<T> {
  columns: ColumnDef<T>[];
  data: T[];
  columnName: string;
  statusByRow?: Record<string, 'En attente' | 'Approuvé' | 'Rejeté' | 'Annulée' | 'Annulation en cours'>;
  onStatusChange?: (
    rowId: string,
    newStatus: 'En attente' | 'Approuvé' | 'Rejeté' | 'Annulée' | 'Annulation en cours',
    matricule: string | undefined,
    commentaireRefus?: string
  ) => void;
  onBatchApprove?: (selectedIds: string[], data: T[]) => Promise<void>;
  onBatchReject?: (selectedIds: string[], data: T[], commentaireRefus: string) => Promise<void>;
  onBatchCancel?: (action: 'ACCEPTER' | 'REFUSER', selectedIds: string[]) => Promise<void>;
  showCheckboxes?: boolean;
  onRowSelectionChange?: (selectedIds: string[]) => void;
  rightSideComponent?: React.ReactNode;
  pageIndex: number;
  pageSize: number;
  totalCount: number;
  onPageChange: (newPageIndex: number) => void;
  refetchData: () => Promise<void>;
}

const BatchApproveDialog: React.FC<{
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onConfirm: () => Promise<void>;
  employeesWithNegativeBalance: { nom: string; prenom: string }[];
}> = ({ open, onOpenChange, onConfirm, employeesWithNegativeBalance }) => {
  const [isSubmitting, setIsSubmitting] = useState(false);

  const handleConfirm = async () => {
    try {
      setIsSubmitting(true);
      await onConfirm();
      onOpenChange(false);
    } catch (error: any) {
      toast({
        title: 'Erreur',
        description: error.message || 'Une erreur est survenue lors de l’approbation.',
        variant: 'destructive',
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={(open) => {
      onOpenChange(open);
    }}>
      <DialogPortal>
        <DialogOverlay className="fixed inset-0 bg-black/50 z-40" />
        <DialogContent className="fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-white rounded-md p-6 z-50 max-w-md w-full border border-gray-400">
          <DialogTitle className="text-lg font-semibold text-center text-[#8B0000]">
            Attention : Solde de congé négatif
          </DialogTitle>
          <div className="grid gap-4 py-4">
            <p className="text-black text-sm">
              Les employés suivants ont un solde de congé négatif. Voulez-vous confirmer l’approbation de ces demandes ?
            </p>
            <ul className="list-disc pl-5 text-black text-sm">
              {employeesWithNegativeBalance.map((employee, index) => (
                <li key={index}>{`${employee.nom} ${employee.prenom}`}</li>
              ))}
            </ul>
          </div>
          <div className="flex justify-between">
            <Button
              onClick={handleConfirm}
              className="px-3 py-0.5 text-xs bg-green-200 text-green-800 rounded h-7"
              disabled={isSubmitting}
            >
              {isSubmitting ? 'En cours...' : 'Confirmer'}
            </Button>
            <DialogClose asChild>
              <Button
                className="px-3 py-0.5 text-xs bg-gray-400 text-white rounded h-7"
                disabled={isSubmitting}
              >
                Annuler
              </Button>
            </DialogClose>
          </div>
        </DialogContent>
      </DialogPortal>
    </Dialog>
  );
};

const BatchRejectDialog: React.FC<{
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onConfirm: (reasons: Record<string, string>) => Promise<void>;
  selectedEmployees: { id: string; nom: string; prenom: string }[];
}> = ({ open, onOpenChange, onConfirm, selectedEmployees }) => {
  // Memoize the initial reasons to prevent reinitialization on selectedEmployees change
  const initialReasons = useMemo(
    () =>
      selectedEmployees.reduce(
        (acc, emp) => ({ ...acc, [emp.id]: '' }),
        {} as Record<string, string>
      ),
    [selectedEmployees]
  );

  const [reasons, setReasons] = useState<Record<string, string>>(initialReasons);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Reset reasons when the dialog opens or selectedEmployees changes
  useEffect(() => {
    setReasons(initialReasons);
  }, [initialReasons, open]);

  const handleReasonChange = (id: string, value: string) => {
    setReasons((prev) => ({ ...prev, [id]: value }));
    setError(null);
  };

  const handleConfirm = async () => {
    const missingReasons = selectedEmployees.filter((emp) => !reasons[emp.id]?.trim());
    if (missingReasons.length > 0) {
      setError('Une raison de refus est obligatoire pour chaque employé.');
      return;
    }

    try {
      setIsSubmitting(true);
      setError(null);
      await onConfirm(reasons);
      onOpenChange(false);
    } catch (error: any) {
      setError(error.message || 'Une erreur est survenue lors du refus.');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog
      open={open}
      onOpenChange={(open) => {
        onOpenChange(open);
        if (!open) {
          setReasons(initialReasons);
          setError(null);
        }
      }}
    >
      <DialogPortal>
        <DialogOverlay className="fixed inset-0 bg-black/50 z-40" />
        <DialogContent className="fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-white rounded-md p-6 z-50 max-w-md w-full border border-gray-400">
          <DialogTitle className="text-lg font-semibold text-center text-[#8B0000]">
            Confirmer le refus en masse
          </DialogTitle>
          <div className="grid gap-4 py-4">
            <p className="text-black text-sm">
              Veuillez entrer une raison de refus pour chaque employé (obligatoire).
            </p>
            {selectedEmployees.map((employee) => (
              <div key={employee.id} className="grid grid-cols-4 items-center gap-4">
                <label
                  htmlFor={`reason-${employee.id}`}
                  className="text-right text-sm font-medium text-gray-700 col-span-1 truncate"
                  title={`${employee.nom} ${employee.prenom}`}
                >
                  {`${employee.nom} ${employee.prenom}`}
                </label>
                <input
                  type="text"
                  id={`reason-${employee.id}`}
                  placeholder="Raison du refus"
                  value={reasons[employee.id] ?? ''} // Ensure value is always defined
                  onChange={(e) => handleReasonChange(employee.id, e.target.value)}
                  className="col-span-3 border rounded p-2 text-sm"
                  required
                  disabled={isSubmitting}
                />
              </div>
            ))}
            {error && <p className="text-red-600 text-sm">{error}</p>}
          </div>
          <div className="flex justify-between">
            <Button
              onClick={handleConfirm}
              className="px-3 py-0.5 text-xs bg-green-200 text-green-800 rounded h-7"
              disabled={isSubmitting}
            >
              {isSubmitting ? 'En cours...' : 'Confirmer'}
            </Button>
            <DialogClose asChild>
              <Button
                className="px-3 py-0.5 text-xs bg-gray-400 text-white rounded h-7"
                disabled={isSubmitting}
              >
                Annuler
              </Button>
            </DialogClose>
          </div>
        </DialogContent>
      </DialogPortal>
    </Dialog>
  );
};

const BatchCancelDialog: React.FC<{
  open: boolean;
  onOpenChange: (open: boolean) => void;
  onConfirm: (action: 'ACCEPTER' | 'REFUSER', selectedIds: string[]) => Promise<void>;
  selectedRequests: { id: string; nom: string; prenom: string; raisonAnnulation: string }[];
}> = ({ open, onOpenChange, onConfirm, selectedRequests }) => {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleAction = async (action: 'ACCEPTER' | 'REFUSER') => {
    try {
      setIsSubmitting(true);
      setError(null);
      const selectedIds = selectedRequests.map((request) => request.id);
      await onConfirm(action, selectedIds);
      onOpenChange(false);
    } catch (error: any) {
      setError(error.message || 'Une erreur est survenue lors du traitement des demandes.');
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog
      open={open}
      onOpenChange={(open) => {
        onOpenChange(open);
        if (!open) {
          setError(null);
        }
      }}
    >
      <DialogPortal>
        <DialogOverlay className="fixed inset-0 bg-black/50 z-40" />
        <DialogContent className="fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-white rounded-md p-6 z-50 max-w-md w-full border border-gray-400">
          <DialogTitle className="text-lg font-semibold text-center text-[#8B0000]">
            Traitement des demandes d&apos;annulation
          </DialogTitle>
          <div className="grid gap-4 py-4 max-h-80 overflow-y-auto">
            <p className="text-black text-sm">
              Veuillez examiner les raisons de demande d&apos;annulation pour chaque employé.
            </p>
            {selectedRequests.map((request) => (
              <div key={request.id} className="grid gap-2 border-b pb-4 last:border-b-0">
                <div className="flex items-center gap-2">
                  <span className="text-sm font-medium text-gray-700">
                    {`${request.nom} ${request.prenom}`} :
                  </span>
                  <p className="text-black text-sm">{request.raisonAnnulation}</p>
                </div>
              </div>
            ))}
            {error && <p className="text-red-600 text-sm">{error}</p>}
          </div>
          <div className="flex justify-between">
            <Button
              onClick={() => handleAction('ACCEPTER')}
              className="px-3 py-0.5 text-xs bg-green-200 text-green-800 rounded h-7"
              disabled={isSubmitting}
            >
              {isSubmitting ? 'En cours...' : 'Approuver tout'}
            </Button>
            <Button
              onClick={() => handleAction('REFUSER')}
              className="px-3 py-0.5 text-xs bg-red-200 text-red-800 rounded h-7"
              disabled={isSubmitting}
            >
              {isSubmitting ? 'En cours...' : 'Rejeter tout'}
            </Button>
          </div>
        </DialogContent>
      </DialogPortal>
    </Dialog>
  );
};

const   CustomTable = <T extends RowData>({
  columns,
  data,
  columnName,
  statusByRow,
  onStatusChange,
  onBatchApprove,
  onBatchReject,
  onBatchCancel,
  showCheckboxes = false,
  onRowSelectionChange,
  rightSideComponent,
  pageIndex,
  pageSize,
  totalCount,
  onPageChange,
  refetchData,
}: DynamicTableProps<T>) => {
  const [rowSelection, setRowSelection] = useState<Record<string, boolean>>({});
  const [soldeCongeMap, setSoldeCongeMap] = useState<Record<string, number>>({});
  const [batchRejectDialogOpen, setBatchRejectDialogOpen] = useState(false);
  const [batchApproveDialogOpen, setBatchApproveDialogOpen] = useState(false);
  const [batchCancelDialogOpen, setBatchCancelDialogOpen] = useState(false);
  const [employeesWithNegativeBalance, setEmployeesWithNegativeBalance] = useState<
    { nom: string; prenom: string }[]
  >([]);

  useEffect(() => {
    setRowSelection((prev) => {
      const validSelections: Record<string, boolean> = {};
      data.forEach((row) => {
        if (prev[row.id]) {
          validSelections[row.id] = prev[row.id];
        }
      });
      console.log('Updated rowSelection after data change:', validSelections);
      return validSelections;
    });
    const initialSoldeMap: Record<string, number> = {};
    data.forEach((row) => {
      if (row.utilisateur) {
        initialSoldeMap[row.id] = row.utilisateur.soldeConge;
      }
    });
    setSoldeCongeMap(initialSoldeMap);
  }, [data]);

  const handleSoldeUpdate = (rowId: string, newSolde: number) => {
    setSoldeCongeMap((prevMap) => ({
      ...prevMap,
      [rowId]: newSolde,
    }));
  };

  const checkboxColumn: ColumnDef<T> = {
    id: 'select',
    header: ({ table }) => (
      <input
        type="checkbox"
        checked={table.getIsAllRowsSelected()}
        onChange={table.getToggleAllRowsSelectedHandler()}
      />
    ),
    cell: ({ row }) => {
      console.log(`Row ${row.original.id} - isSelected: ${row.getIsSelected()}`);
      return (
        <input
          type="checkbox"
          checked={row.getIsSelected()}
          onChange={row.getToggleSelectedHandler()}
          disabled={['Approuvé', 'Rejeté', 'Annulée'].includes(statusByRow?.[row.original.id] || '')}
        />
      );
    },
    enableSorting: false,
    enableHiding: false,
  };

  const combinedColumns = showCheckboxes ? [checkboxColumn, ...columns] : columns;

  const table = useReactTable({
    data,
    columns: combinedColumns,
    getCoreRowModel: getCoreRowModel(),
    getSortedRowModel: getSortedRowModel(),
    getFilteredRowModel: getFilteredRowModel(),
    state: {
      rowSelection,
    },
    onRowSelectionChange: (updater) => {
      setRowSelection((prev) => {
        const newSelection = typeof updater === 'function' ? updater(prev) : updater;
        console.log('New rowSelection:', newSelection);
        if (onRowSelectionChange) {
          const selectedIds = table.getSelectedRowModel().rows.map((row) => row.original.id);
          console.log('Selected IDs in CustomTable:', selectedIds);
          console.log('Number of selected rows:', table.getSelectedRowModel().rows.length);
          // Defer the onRowSelectionChange call to avoid state updates during render
          setTimeout(() => {
            onRowSelectionChange(selectedIds);
          }, 0);
        }
        return newSelection;
      });
    },
    enableRowSelection: showCheckboxes,
    getRowId: (row) => row.id,
  });

  const totalPages = Math.ceil(totalCount / pageSize);

  const handlePreviousPage = () => {
    if (pageIndex > 0) {
      onPageChange(pageIndex - 1);
    }
  };

  const handleNextPage = () => {
    if (pageIndex < totalPages - 1) {
      onPageChange(pageIndex + 1);
    }
  };

  const handleBatchApprove = async () => {
    const selectedIds = table.getSelectedRowModel().rows.map((row) => row.original.id);
    if (selectedIds.length === 0) {
      toast({
        title: 'Erreur',
        description: 'Aucune demande sélectionnée.',
        variant: 'destructive',
      });
      return;
    }

    const validIds = selectedIds.filter((id) => {
      const status = statusByRow?.[id];
      return status === 'En attente' || status === 'Annulation en cours';
    });

    const invalidIds = selectedIds.filter((id) => !validIds.includes(id));

    if (validIds.length === 0) {
      toast({
        title: 'Erreur',
        description:
          'Aucune demande valide sélectionnée. Les demandes doivent être "En attente" ou "Annulation en cours".',
        variant: 'destructive',
      });
      return;
    }

    if (invalidIds.length > 0) {
      toast({
        title: 'Avertissement',
        description: `${invalidIds.length} demande(s) déjà approuvée(s), rejetée(s) ou annulée(s) ont été ignorée(s).`,
        variant: 'default',
      });
    }

    const negativeBalanceEmployees = table
      .getSelectedRowModel()
      .rows.filter(
        (row) => validIds.includes(row.original.id) && row.original.utilisateur && row.original.utilisateur?.soldeConge < 0
      )
      .map((row) => ({
        nom: row.original.utilisateur?.nom || '',
        prenom: row.original.utilisateur?.prenom || '',
      }));

    if (negativeBalanceEmployees.length > 0) {
      setEmployeesWithNegativeBalance(negativeBalanceEmployees);
      setBatchApproveDialogOpen(true);
      return;
    }

    try {
      if (onBatchApprove) {
        await onBatchApprove(validIds, data);
      }
      await refetchData();
      setRowSelection({});
      toast({
        title: 'Approbation en masse',
        description: `${validIds.length} demande(s) ont été approuvées avec succès.`,
      });
    } catch (error: any) {
      toast({
        title: 'Erreur',
        description: error.message || 'Impossible d’approuver les demandes sélectionnées.',
        variant: 'destructive',
      });
    }
  };

  const confirmBatchApprove = async () => {
    const validIds = table
      .getSelectedRowModel()
      .rows.map((row) => row.original.id)
      .filter((id) => {
        const status = statusByRow?.[id];
        return status === 'En attente' || status === 'Annulation en cours';
      });

    try {
      if (onBatchApprove) {
        await onBatchApprove(validIds, data);
      }
      await refetchData();
      setRowSelection({});
      toast({
        title: 'Approbation en masse',
        description: `${validIds.length} demande(s) ont été approuvées avec succès.`,
      });
    } catch (error: any) {
      toast({
        title: 'Erreur',
        description: error.message || 'Impossible d’approuver les demandes sélectionnées.',
        variant: 'destructive',
      });
    }
  };

  const handleBatchReject = async (reasons: Record<string, string>) => {
    const selectedIds = table.getSelectedRowModel().rows.map((row) => row.original.id);
    if (selectedIds.length === 0) {
      toast({
        title: 'Erreur',
        description: 'Aucune demande sélectionnée.',
        variant: 'destructive',
      });
      return;
    }

    const validIds = selectedIds.filter((id) => {
      const status = statusByRow?.[id];
      return status === 'En attente' || status === 'Annulation en cours';
    });

    const invalidIds = selectedIds.filter((id) => !validIds.includes(id));

    if (validIds.length === 0) {
      toast({
        title: 'Erreur',
        description:
          'Aucune demande valide sélectionnée. Les demandes doivent être "En attente" ou "Annulation en cours".',
        variant: 'destructive',
      });
      return;
    }

    if (invalidIds.length > 0) {
      toast({
        title: 'Avertissement',
        description: `${invalidIds.length} demande(s) déjà approuvée(s), rejetée(s) ou annulée(s) ont été ignorée(s).`,
        variant: 'default',
      });
    }

    try {
      if (onBatchReject) {
        for (const id of validIds) {
          const rowData = data.find((row) => row.id === id);
          if (rowData) {
            await onBatchReject([id], data, reasons[id]);
          }
        }
      }
      await refetchData();
      setRowSelection({});
      toast({
        title: 'Rejet en masse',
        description: `${validIds.length} demande(s) ont été rejetées avec succès.`,
      });
    } catch (error: any) {
      toast({
        title: 'Erreur',
        description: error.message || 'Impossible de rejeter les demandes sélectionnées.',
        variant: 'destructive',
      });
    }
  };

  // Determine if any selected rows have "Annulation en cours" status
  const hasCancelRequests = table.getSelectedRowModel().rows.some(
    (row) => statusByRow?.[row.original.id] === 'Annulation en cours'
  );

  const selectedCancelRequests = table
    .getSelectedRowModel()
    .rows.filter((row) => statusByRow?.[row.original.id] === 'Annulation en cours')
    .map((row) => ({
      id: row.original.id,
      nom: row.original.utilisateur?.nom || '',
      prenom: row.original.utilisateur?.prenom || '',
      raisonAnnulation: row.original.raison_annulation || '',
    }));
  console.log('Selected cancel requests for BatchCancelDialog:', selectedCancelRequests);

  return (
    <div className="w-full">
      <div className="flex items-center justfy-between py-4">
        {table.getSelectedRowModel().rows.length > 0 && (
          <div className="flex space-x-2">
            {hasCancelRequests ? (
              // Show only "Traiter Annulations" button if any selected row is "Annulation en cours"
              <Button
                onClick={() => setBatchCancelDialogOpen(true)}
                className="px-3 py-0.5 text-xs bg-yellow-200 text-yellow-800 rounded h-7"
              >
                Traiter Annulations (
                {table.getSelectedRowModel().rows.filter(
                  (row) => statusByRow?.[row.original.id] === 'Annulation en cours'
                ).length}
                )
              </Button>
            ) : (
              // Show "Approuver" and "Rejeter" buttons only if no "Annulation en cours" rows are selected
              <>
                <Button
                  onClick={handleBatchApprove}
                  className="px-3 py-0.5 text-xs bg-green-200 text-green-800 rounded h-7"
                >
                  Approuver ({table.getSelectedRowModel().rows.length})
                </Button>
                <Button
                  onClick={() => setBatchRejectDialogOpen(true)}
                  className="px-3 py-0.5 text-xs bg-red-200 text-red-800 rounded h-7"
                >
                  Rejeter ({table.getSelectedRowModel().rows.length})
                </Button>
              </>
            )}
          </div>
        )}
{rightSideComponent && (
  <div className="flex justify-end items-center w-full">
    {rightSideComponent}
  </div>
)}      </div>

      <div className="rounded-md border table-container" style={{ maxHeight: '400px', overflowY: 'auto' }}>
        <Table>
          <TableHeader>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => (
                  <TableHead key={header.id}>
                    {header.isPlaceholder
                      ? null
                      : flexRender(header.column.columnDef.header, header.getContext())}
                  </TableHead>
                ))}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {table.getRowModel().rows?.length ? (
              table.getRowModel().rows.map((row) => (
                <TableRow
                  key={row.id}
                  data-state={row.getIsSelected() && 'selected'}
                  className={`table-row ${row.getIsSelected() ? 'bg-gray-200' : ''}`}
                >
                  {row.getVisibleCells().map((cell) => (
                    <TableCell key={cell.id}>
                      {cell.column.id === 'Validation' ? (
                        <StatusBadge
                          statut={statusByRow?.[row.original.id] || 'En attente'}
                          rowId={row.original.id}
                          matricule={String(row.original.utilisateur?.matriculeSegula)}
                          soldeConge={row.original.utilisateur?.soldeConge || 0}
                          nomUtilisateur={`${row.original.utilisateur?.nom || ''} ${
                            row.original.utilisateur?.prenom || ''
                          }`}
                          onStatusChange={(rowId, newStatus, commentaireRefus) =>
                            onStatusChange?.(
                              rowId,
                              newStatus,
                              row.original.utilisateur?.matriculeSegula,
                              commentaireRefus
                            )
                          }
                          raisonAnnulation={row.original.raison_annulation}
                          onSoldeUpdate={async (newSolde) => {
                            handleSoldeUpdate(row.original.id, newSolde);
                            await refetchData();
                          }}
                        />
                      ) : (
                        flexRender(cell.column.columnDef.cell, cell.getContext())
                      )}
                    </TableCell>
                  ))}
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={columns.length} className="h-24 text-center">
                  Aucun résultat trouvé.
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>

      <div className="flex items-center justify-end py-4">
        <div className="flex items-center gap-2">
          <button
            onClick={handlePreviousPage}
            disabled={pageIndex === 0}
            className={`px-3 py-2 rounded-md ${
              pageIndex === 0 ? 'opacity-50 cursor-not-allowed' : 'hover:bg-gray-100'
            }`}
          >
            précédent
          </button>
          <div className="flex items-center gap-1">
            {Array.from({ length: Math.min(totalPages, 5) }, (_, index) => {
              let pageNumber;
              if (totalPages <= 5) {
                pageNumber = index;
              } else {
                const middleIndex = Math.min(Math.max(pageIndex, 2), totalPages - 3);
                pageNumber = index + Math.max(0, middleIndex - 2);
              }
              return (
                <button
                  key={pageNumber}
                  onClick={() => onPageChange(pageNumber)}
                  className={`px-3 py-2 rounded-md ${
                    pageNumber === pageIndex ? 'bg-primary text-white' : 'hover:bg-gray-100'
                  }`}
                >
                  {pageNumber + 1}
                </button>
              );
            })}
          </div>
          <button
            onClick={handleNextPage}
            disabled={pageIndex >= totalPages - 1}
            className={`px-3 py-2 rounded-md ${
              pageIndex >= totalPages - 1 ? 'opacity-50 cursor-not-allowed' : 'hover:bg-gray-100'
            }`}
          >
            Suivant
          </button>
        </div>
      </div>

      <BatchRejectDialog
        open={batchRejectDialogOpen}
        onOpenChange={setBatchRejectDialogOpen}
        onConfirm={handleBatchReject}
        selectedEmployees={table
          .getSelectedRowModel()
          .rows.filter((row) => {
            const status = statusByRow?.[row.original.id];
            return status === 'En attente' || status === 'Annulation en cours';
          })
          .map((row) => ({
            id: row.original.id,
            nom: row.original.utilisateur?.nom || '',
            prenom: row.original.utilisateur?.prenom || '',
          }))}
      />

      <BatchApproveDialog
        open={batchApproveDialogOpen}
        onOpenChange={setBatchApproveDialogOpen}
        onConfirm={confirmBatchApprove}
        employeesWithNegativeBalance={employeesWithNegativeBalance}
      />

      <BatchCancelDialog
        open={batchCancelDialogOpen}
        onOpenChange={setBatchCancelDialogOpen}
        onConfirm={(action, selectedIds) => onBatchCancel?.(action, selectedIds) || Promise.resolve()}
        selectedRequests={selectedCancelRequests}
      />
    </div>
  );
};

export default CustomTable;