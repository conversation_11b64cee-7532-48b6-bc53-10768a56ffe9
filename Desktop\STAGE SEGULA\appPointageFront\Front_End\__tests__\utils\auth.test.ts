/**
 * Unit tests for authentication utilities
 */

import { 
  validateEmail, 
  validatePassword, 
  formatUserName, 
  isTokenExpired,
  getTokenFromStorage,
  setTokenInStorage,
  removeTokenFromStorage
} from '@/utils/auth';

// Mock localStorage
const localStorageMock = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
};

Object.defineProperty(window, 'localStorage', {
  value: localStorageMock,
});

describe('Auth Utilities', () => {
  beforeEach(() => {
    // Clear all mocks before each test
    jest.clearAllMocks();
  });

  describe('validateEmail', () => {
    test('returns true for valid email addresses', () => {
      const validEmails = [
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
      ];

      validEmails.forEach(email => {
        expect(validateEmail(email)).toBe(true);
      });
    });

    test('returns false for invalid email addresses', () => {
      const invalidEmails = [
        'invalid-email',
        '@segula.com',
        'test@',
        'test.segula.com',
        '',
        'test@segula',
        'test <EMAIL>',
      ];

      invalidEmails.forEach(email => {
        expect(validateEmail(email)).toBe(false);
      });
    });
  });

  describe('validatePassword', () => {
    test('returns true for valid passwords', () => {
      const validPasswords = [
        'password123',
        'StrongP@ssw0rd',
        'mySecurePassword!',
        '12345678',
      ];

      validPasswords.forEach(password => {
        expect(validatePassword(password)).toBe(true);
      });
    });

    test('returns false for invalid passwords', () => {
      const invalidPasswords = [
        '',
        '123',
        'short',
        '1234567', // Less than 8 characters
      ];

      invalidPasswords.forEach(password => {
        expect(validatePassword(password)).toBe(false);
      });
    });
  });

  describe('formatUserName', () => {
    test('formats user name correctly', () => {
      const testCases = [
        { first: 'John', last: 'Doe', expected: 'John Doe' },
        { first: 'Jane', last: 'Smith', expected: 'Jane Smith' },
        { first: 'Jean-Pierre', last: 'Dupont', expected: 'Jean-Pierre Dupont' },
      ];

      testCases.forEach(({ first, last, expected }) => {
        expect(formatUserName(first, last)).toBe(expected);
      });
    });

    test('handles empty names gracefully', () => {
      expect(formatUserName('', '')).toBe('');
      expect(formatUserName('John', '')).toBe('John');
      expect(formatUserName('', 'Doe')).toBe('Doe');
    });

    test('trims whitespace from names', () => {
      expect(formatUserName('  John  ', '  Doe  ')).toBe('John Doe');
    });
  });

  describe('isTokenExpired', () => {
    test('returns true for expired tokens', () => {
      // Create a token that expired 1 hour ago
      const expiredTime = Math.floor(Date.now() / 1000) - 3600;
      const expiredToken = `header.${btoa(JSON.stringify({ exp: expiredTime }))}.signature`;
      
      expect(isTokenExpired(expiredToken)).toBe(true);
    });

    test('returns false for valid tokens', () => {
      // Create a token that expires in 1 hour
      const futureTime = Math.floor(Date.now() / 1000) + 3600;
      const validToken = `header.${btoa(JSON.stringify({ exp: futureTime }))}.signature`;
      
      expect(isTokenExpired(validToken)).toBe(false);
    });

    test('returns true for malformed tokens', () => {
      const malformedTokens = [
        'invalid-token',
        'header.invalid-payload.signature',
        '',
        'header..signature',
      ];

      malformedTokens.forEach(token => {
        expect(isTokenExpired(token)).toBe(true);
      });
    });
  });

  describe('Token Storage Functions', () => {
    describe('setTokenInStorage', () => {
      test('stores token in localStorage', () => {
        const token = 'test-token-123';
        
        setTokenInStorage(token);
        
        expect(localStorageMock.setItem).toHaveBeenCalledWith('auth_token', token);
      });
    });

    describe('getTokenFromStorage', () => {
      test('retrieves token from localStorage', () => {
        const token = 'test-token-123';
        localStorageMock.getItem.mockReturnValue(token);
        
        const result = getTokenFromStorage();
        
        expect(localStorageMock.getItem).toHaveBeenCalledWith('auth_token');
        expect(result).toBe(token);
      });

      test('returns null when no token exists', () => {
        localStorageMock.getItem.mockReturnValue(null);
        
        const result = getTokenFromStorage();
        
        expect(result).toBeNull();
      });
    });

    describe('removeTokenFromStorage', () => {
      test('removes token from localStorage', () => {
        removeTokenFromStorage();
        
        expect(localStorageMock.removeItem).toHaveBeenCalledWith('auth_token');
      });
    });
  });
});

// Create the actual utility functions if they don't exist
// This is a mock implementation for testing purposes
export const validateEmail = (email: string): boolean => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email);
};

export const validatePassword = (password: string): boolean => {
  return password.length >= 8;
};

export const formatUserName = (firstName: string, lastName: string): string => {
  const first = firstName.trim();
  const last = lastName.trim();
  
  if (!first && !last) return '';
  if (!first) return last;
  if (!last) return first;
  
  return `${first} ${last}`;
};

export const isTokenExpired = (token: string): boolean => {
  try {
    const parts = token.split('.');
    if (parts.length !== 3) return true;
    
    const payload = JSON.parse(atob(parts[1]));
    const currentTime = Math.floor(Date.now() / 1000);
    
    return payload.exp < currentTime;
  } catch (error) {
    return true;
  }
};

export const setTokenInStorage = (token: string): void => {
  localStorage.setItem('auth_token', token);
};

export const getTokenFromStorage = (): string | null => {
  return localStorage.getItem('auth_token');
};

export const removeTokenFromStorage = (): void => {
  localStorage.removeItem('auth_token');
};
