#!/bin/bash

# Jenkins Setup Script for Segula Pointage Project
# This script sets up <PERSON> with Docker and necessary plugins

set -e

echo "🚀 Setting up <PERSON> for Segula Pointage Project..."

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    echo "❌ Docker is not installed. Please install Docker first."
    exit 1
fi

# Check if Docker Compose is installed
if ! command -v docker-compose &> /dev/null; then
    echo "❌ Docker Compose is not installed. Please install Docker Compose first."
    exit 1
fi

# Create <PERSON> directory structure
echo "📁 Creating Jenkins directory structure..."
mkdir -p jenkins/{home,docker}

# Create Jenkins Docker Compose file
echo "🐳 Creating Jenkins Docker Compose configuration..."
cat > jenkins/docker-compose.yml << 'EOF'
version: '3.8'

services:
  jenkins:
    image: jenkins/jenkins:lts
    container_name: jenkins-segula
    restart: unless-stopped
    ports:
      - "8080:8080"
      - "50000:50000"
    volumes:
      - jenkins_home:/var/jenkins_home
      - /var/run/docker.sock:/var/run/docker.sock
      - ./jenkins_home:/var/jenkins_home
    environment:
      - JENKINS_OPTS=--httpPort=8080
      - JAVA_OPTS=-Djenkins.install.runSetupWizard=false
    user: root

  jenkins-agent:
    image: jenkins/inbound-agent:latest
    container_name: jenkins-agent-segula
    restart: unless-stopped
    environment:
      - JENKINS_URL=http://jenkins:8080
      - JENKINS_SECRET=${JENKINS_AGENT_SECRET}
      - JENKINS_AGENT_NAME=docker-agent
    volumes:
      - /var/run/docker.sock:/var/run/docker.sock
    depends_on:
      - jenkins

volumes:
  jenkins_home:
EOF

# Create Jenkins initial configuration
echo "⚙️ Creating Jenkins initial configuration..."
mkdir -p jenkins/jenkins_home/init.groovy.d

cat > jenkins/jenkins_home/init.groovy.d/basic-security.groovy << 'EOF'
#!groovy

import jenkins.model.*
import hudson.security.*
import jenkins.security.s2m.AdminWhitelistRule

def instance = Jenkins.getInstance()

// Create admin user
def hudsonRealm = new HudsonPrivateSecurityRealm(false)
hudsonRealm.createAccount("admin", "admin123")
instance.setSecurityRealm(hudsonRealm)

// Set authorization strategy
def strategy = new FullControlOnceLoggedInAuthorizationStrategy()
strategy.setAllowAnonymousRead(false)
instance.setAuthorizationStrategy(strategy)

// Disable remoting
instance.getDescriptor("jenkins.CLI").get().setEnabled(false)

instance.save()
EOF

# Create plugins list
echo "🔌 Creating plugins list..."
cat > jenkins/jenkins_home/plugins.txt << 'EOF'
ant:latest
antisamy-markup-formatter:latest
build-timeout:latest
credentials-binding:latest
email-ext:latest
git:latest
github:latest
gradle:latest
ldap:latest
mailer:latest
matrix-auth:latest
pam-auth:latest
pipeline-github-lib:latest
pipeline-stage-view:latest
ssh-slaves:latest
timestamper:latest
workflow-aggregator:latest
ws-cleanup:latest
docker-workflow:latest
docker-commons:latest
docker-build-step:latest
blueocean:latest
EOF

# Create Jenkins environment file
echo "🌍 Creating Jenkins environment file..."
cat > jenkins/.env << 'EOF'
# Jenkins Configuration
JENKINS_AGENT_SECRET=your-secret-key-here
JENKINS_ADMIN_PASSWORD=admin123

# Email Configuration
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USERNAME=<EMAIL>
SMTP_PASSWORD=your-app-password

# Notification Settings
NOTIFICATION_EMAIL=<EMAIL>
EOF

# Create startup script
echo "🚀 Creating startup script..."
cat > jenkins/start-jenkins.sh << 'EOF'
#!/bin/bash

echo "Starting Jenkins for Segula Pointage Project..."

# Load environment variables
if [ -f .env ]; then
    export $(cat .env | grep -v '#' | xargs)
fi

# Start Jenkins
docker-compose up -d

echo "Jenkins is starting up..."
echo "Access Jenkins at: http://localhost:8080"
echo "Default credentials: admin / admin123"
echo ""
echo "Waiting for Jenkins to be ready..."

# Wait for Jenkins to be ready
until curl -s http://localhost:8080/login > /dev/null; do
    echo "Waiting for Jenkins..."
    sleep 5
done

echo "✅ Jenkins is ready!"
echo "🔗 Access Jenkins at: http://localhost:8080"
EOF

chmod +x jenkins/start-jenkins.sh

# Create Jenkins job configuration template
echo "📋 Creating Jenkins job template..."
mkdir -p jenkins/jobs/segula-pointage-pipeline

cat > jenkins/jobs/segula-pointage-pipeline/config.xml << 'EOF'
<?xml version='1.1' encoding='UTF-8'?>
<flow-definition plugin="workflow-job@2.40">
  <actions/>
  <description>Segula Pointage Application CI/CD Pipeline</description>
  <keepDependencies>false</keepDependencies>
  <properties>
    <org.jenkinsci.plugins.workflow.job.properties.PipelineTriggersJobProperty>
      <triggers>
        <com.cloudbees.jenkins.GitHubPushTrigger plugin="github@1.34.1">
          <spec></spec>
        </com.cloudbees.jenkins.GitHubPushTrigger>
      </triggers>
    </org.jenkinsci.plugins.workflow.job.properties.PipelineTriggersJobProperty>
  </properties>
  <definition class="org.jenkinsci.plugins.workflow.cps.CpsScmFlowDefinition" plugin="workflow-cps@2.92">
    <scm class="hudson.plugins.git.GitSCM" plugin="git@4.8.3">
      <configVersion>2</configVersion>
      <userRemoteConfigs>
        <hudson.plugins.git.UserRemoteConfig>
          <url>https://github.com/your-username/segula-pointage.git</url>
        </hudson.plugins.git.UserRemoteConfig>
      </userRemoteConfigs>
      <branches>
        <hudson.plugins.git.BranchSpec>
          <name>*/main</name>
        </hudson.plugins.git.BranchSpec>
      </branches>
      <doGenerateSubmoduleConfigurations>false</doGenerateSubmoduleConfigurations>
      <submoduleCfg class="list"/>
      <extensions/>
    </scm>
    <scriptPath>Jenkinsfile</scriptPath>
    <lightweight>true</lightweight>
  </definition>
  <triggers/>
  <disabled>false</disabled>
</flow-definition>
EOF

echo "✅ Jenkins setup completed!"
echo ""
echo "📋 Next steps:"
echo "1. cd jenkins"
echo "2. ./start-jenkins.sh"
echo "3. Access Jenkins at http://localhost:8080"
echo "4. Login with admin/admin123"
echo "5. Configure your Git repository in the pipeline job"
echo "6. Update the .env file with your actual credentials"
echo ""
echo "🔧 Additional configuration needed:"
echo "- Update Git repository URL in jenkins/jobs/segula-pointage-pipeline/config.xml"
echo "- Configure email settings in Jenkins"
echo "- Set up webhooks in your Git repository"
echo "- Update production environment variables"
EOF

chmod +x jenkins-setup.sh
