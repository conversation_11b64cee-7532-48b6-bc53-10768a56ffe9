# Environment files
.env
.env.local
.env.production
.env.staging
.env.test

# Docker
docker-compose.override.yml
db_data/
prod_db_data/
staging_db_data/
static_files/
media_files/

# <PERSON>
jenkins/jenkins_home/
jenkins/docker/

# SSL certificates
nginx/ssl/
*.pem
*.key
*.crt

# Database
*.sql
*.sqlite3
*.dump

# Logs
*.log
logs/

# OS
.DS_Store
Thumbs.db

# IDE
.vscode/
.idea/
*.swp
*.swo

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
env/
venv/
ENV/
env.bak/
venv.bak/

# Node.js
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.next/
out/

# Django
*.pyc
db.sqlite3
media/
staticfiles/
static/

# Temporary files
*.tmp
*.temp
temp/
tmp/
