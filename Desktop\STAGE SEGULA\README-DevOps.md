# Segula Pointage - DevOps Setup Guide

This guide covers the complete DevOps setup for the Segula Pointage application, including Docker containerization, CI/CD with <PERSON>, and environment management.

## 🏗️ Architecture Overview

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend      │    │    Backend      │    │   Database      │
│   (Next.js)     │◄──►│   (Django)      │◄──►│   (MariaDB)     │
│   Port: 3000    │    │   Port: 8000    │    │   Port: 3306    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
                    ┌─────────────────┐
                    │     Nginx       │
                    │  (Production)   │
                    │  Ports: 80/443  │
                    └─────────────────┘
```

## 📁 Project Structure

```
STAGE SEGULA/
├── appPointage/backend/          # Django backend
├── appPointageFront/Front_End/   # Next.js frontend
├── docker-compose.yml            # Main Docker Compose file
├── docker-compose.production.yml # Production overrides
├── docker-compose.staging.yml    # Staging overrides
├── .env                         # Development environment variables
├── .env.production             # Production environment variables
├── .env.example               # Environment template
├── Jenkinsfile               # CI/CD pipeline
├── jenkins-setup.sh         # Jenkins setup script
├── nginx/                  # Nginx configuration
│   └── nginx.conf         # Production reverse proxy config
└── README-DevOps.md      # This file
```

## 🚀 Quick Start

### Development Environment

1. **Clone and setup environment:**
   ```bash
   git clone <your-repo>
   cd "STAGE SEGULA"
   ```

2. **Start all services:**
   ```bash
   docker-compose up -d
   ```

3. **Access the application:**
   - Frontend: http://localhost:3000
   - Backend: http://localhost:8000
   - Database: localhost:3308

### Production Deployment

1. **Setup production environment:**
   ```bash
   cp .env.production .env
   # Edit .env with your production values
   ```

2. **Deploy with production configuration:**
   ```bash
   docker-compose -f docker-compose.yml -f docker-compose.production.yml up -d
   ```

## 🔧 Environment Configuration

### Environment Files

- **`.env`** - Current environment (copied from .env.production or development)
- **`.env.production`** - Production settings
- **`.env.example`** - Template for new environments

### Key Environment Variables

```bash
# Database
DB_ROOT_PASSWORD=your_secure_password
DB_NAME=Access_prod
DB_USER=prod_user
DB_PASSWORD=your_secure_password

# Django
SECRET_KEY=your_django_secret_key
DJANGO_DEBUG=false
DJANGO_ALLOWED_HOSTS=your-domain.com

# Frontend
NEXT_PUBLIC_API_URL=http://backend:8000

# Ports
BACKEND_PORT=8000
FRONTEND_PORT=3000
```

## 🐳 Docker Services

### Database (MariaDB)
- **Image:** mariadb:10.11
- **Port:** 3308:3306
- **Volume:** db_data
- **Health Check:** MySQL ping

### Backend (Django)
- **Build:** ./appPointage/backend
- **Port:** 8000:8000
- **Dependencies:** Database health check
- **Settings:** Docker-specific settings file

### Frontend (Next.js)
- **Build:** ./appPointageFront/Front_End
- **Port:** 3000:3000
- **Dependencies:** Backend service
- **Environment:** API URL configuration

### Nginx (Production Only)
- **Image:** nginx:alpine
- **Ports:** 80:80, 443:443
- **Features:** SSL, reverse proxy, static files, rate limiting

## 🔄 CI/CD Pipeline (Jenkins)

### Setup Jenkins

1. **Run the setup script:**
   ```bash
   chmod +x jenkins-setup.sh
   ./jenkins-setup.sh
   ```

2. **Start Jenkins:**
   ```bash
   cd jenkins
   ./start-jenkins.sh
   ```

3. **Access Jenkins:**
   - URL: http://localhost:8080
   - Username: admin
   - Password: admin123

### Pipeline Stages

1. **Checkout** - Get source code
2. **Environment Setup** - Configure environment
3. **Build Images** - Build Docker containers
4. **Run Tests** - Execute test suites
5. **Security Scan** - Vulnerability scanning
6. **Deploy to Staging** - Deploy develop branch
7. **Deploy to Production** - Deploy main branch
8. **Health Check** - Verify deployment

### Branch Strategy

- **`main`** → Production deployment
- **`develop`** → Staging deployment
- **Feature branches** → Build and test only

## 🔒 Security Features

### Nginx Security
- SSL/TLS encryption
- Security headers (HSTS, XSS protection)
- Rate limiting on API endpoints
- CORS configuration

### Docker Security
- Non-root users where possible
- Resource limits in production
- Vulnerability scanning with Trivy
- Secrets management via environment variables

## 📊 Monitoring & Health Checks

### Health Check Endpoints
- **Backend:** `GET /login/` (should return 405 Method Not Allowed)
- **Frontend:** `GET /` (should return 200 OK)
- **Nginx:** `GET /health` (should return 200 "healthy")

### Docker Health Checks
- Database: MySQL ping command
- Automatic restart policies
- Resource monitoring

## 🛠️ Maintenance Commands

### Development
```bash
# Start services
docker-compose up -d

# View logs
docker-compose logs -f [service_name]

# Restart a service
docker-compose restart [service_name]

# Stop all services
docker-compose down

# Clean up (remove volumes)
docker-compose down -v
```

### Production
```bash
# Deploy production
docker-compose -f docker-compose.yml -f docker-compose.production.yml up -d

# Update images
docker-compose pull
docker-compose up -d

# Backup database
docker-compose exec db mysqldump -u root -p Access_prod > backup.sql
```

### Troubleshooting
```bash
# Check container status
docker-compose ps

# View container logs
docker-compose logs [service_name]

# Execute commands in container
docker-compose exec [service_name] bash

# Check resource usage
docker stats
```

## 🔧 Configuration Customization

### Adding New Environment Variables

1. **Add to `.env` file:**
   ```bash
   NEW_VARIABLE=value
   ```

2. **Update docker-compose.yml:**
   ```yaml
   environment:
     - NEW_VARIABLE=${NEW_VARIABLE}
   ```

3. **Restart services:**
   ```bash
   docker-compose up -d
   ```

### Scaling Services

```bash
# Scale backend to 3 instances
docker-compose up -d --scale backend=3

# Scale with load balancer (production)
docker-compose -f docker-compose.yml -f docker-compose.production.yml up -d --scale backend=3
```

## 📞 Support

For issues or questions:
1. Check the logs: `docker-compose logs`
2. Verify environment variables in `.env`
3. Ensure all required ports are available
4. Check Docker and Docker Compose versions

## 🔄 Updates and Maintenance

### Regular Maintenance
- Update base images monthly
- Review and rotate secrets quarterly
- Monitor resource usage
- Update dependencies regularly

### Backup Strategy
- Database: Daily automated backups
- Code: Git repository
- Configuration: Environment files in secure storage
