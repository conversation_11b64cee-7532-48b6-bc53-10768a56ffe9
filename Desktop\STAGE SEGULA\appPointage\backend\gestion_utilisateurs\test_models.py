"""
Unit tests for User model and related models in gestion_utilisateurs app
"""

from django.test import TestCase
from django.core.exceptions import ValidationError
from django.db import IntegrityError
from django.contrib.auth import get_user_model
from .models import Site, Role, User
from datetime import date, datetime

User = get_user_model()


class SiteModelTest(TestCase):
    """Test cases for Site model"""
    
    def setUp(self):
        """Set up test data"""
        self.site_data = {
            'nom': 'Site Test',
            'adresse': '123 Rue Test',
            'ville': 'Tunis',
            'codePostal': '1000'
        }
    
    def test_site_creation(self):
        """Test creating a site"""
        site = Site.objects.create(**self.site_data)
        self.assertEqual(site.nom, 'Site Test')
        self.assertEqual(site.ville, 'Tunis')
        self.assertEqual(str(site), 'Site Test')
    
    def test_site_str_method(self):
        """Test site string representation"""
        site = Site.objects.create(**self.site_data)
        self.assertEqual(str(site), 'Site Test')


class RoleModelTest(TestCase):
    """Test cases for Role model"""
    
    def setUp(self):
        """Set up test data"""
        self.role_data = {
            'nom': 'Développeur',
            'description': 'Développeur logiciel'
        }
    
    def test_role_creation(self):
        """Test creating a role"""
        role = Role.objects.create(**self.role_data)
        self.assertEqual(role.nom, 'Développeur')
        self.assertEqual(role.description, 'Développeur logiciel')
        self.assertEqual(str(role), 'Développeur')
    
    def test_role_str_method(self):
        """Test role string representation"""
        role = Role.objects.create(**self.role_data)
        self.assertEqual(str(role), 'Développeur')


class UserModelTest(TestCase):
    """Test cases for User model"""
    
    def setUp(self):
        """Set up test data"""
        self.site = Site.objects.create(
            nom='Site Test',
            adresse='123 Rue Test',
            ville='Tunis',
            codePostal='1000'
        )
        
        self.role = Role.objects.create(
            nom='Développeur',
            description='Développeur logiciel'
        )
        
        self.user_data = {
            'email': '<EMAIL>',
            'first_name': 'John',
            'last_name': 'Doe',
            'matriculeSegula': 12345,
            'matriculeCnss': 67890,
            'cin': 11223344,
            'telephone': 20123456,
            'dateNaissance': date(1990, 1, 1),
            'statusSocial': 'Célibataire',
            'sexe': 'Homme',
            'nbreEnfant': 0,
            'site': self.site,
            'role': self.role
        }
    
    def test_user_creation(self):
        """Test creating a user"""
        user = User.objects.create_user(
            password='testpass123',
            **self.user_data
        )
        
        self.assertEqual(user.email, '<EMAIL>')
        self.assertEqual(user.first_name, 'John')
        self.assertEqual(user.last_name, 'Doe')
        self.assertEqual(user.matriculeSegula, 12345)
        self.assertEqual(user.site, self.site)
        self.assertEqual(user.role, self.role)
        self.assertTrue(user.check_password('testpass123'))
    
    def test_user_email_unique(self):
        """Test that email must be unique"""
        User.objects.create_user(
            password='testpass123',
            **self.user_data
        )
        
        # Try to create another user with same email
        with self.assertRaises(IntegrityError):
            User.objects.create_user(
                email='<EMAIL>',
                password='testpass456',
                first_name='Jane',
                last_name='Smith'
            )
    
    def test_user_matricule_segula_unique(self):
        """Test that matriculeSegula must be unique"""
        User.objects.create_user(
            password='testpass123',
            **self.user_data
        )
        
        # Try to create another user with same matriculeSegula
        user_data_2 = self.user_data.copy()
        user_data_2['email'] = '<EMAIL>'
        
        with self.assertRaises(IntegrityError):
            User.objects.create_user(
                password='testpass456',
                **user_data_2
            )
    
    def test_user_cin_unique(self):
        """Test that CIN must be unique"""
        User.objects.create_user(
            password='testpass123',
            **self.user_data
        )
        
        # Try to create another user with same CIN
        user_data_2 = self.user_data.copy()
        user_data_2['email'] = '<EMAIL>'
        user_data_2['matriculeSegula'] = 54321
        
        with self.assertRaises(IntegrityError):
            User.objects.create_user(
                password='testpass456',
                **user_data_2
            )
    
    def test_user_status_social_choices(self):
        """Test status social field choices"""
        valid_choices = ['Célibataire', 'Marié(e)', 'Divorcé(e)']
        
        for choice in valid_choices:
            user_data = self.user_data.copy()
            user_data['email'] = f'test_{choice}@segula.com'
            user_data['matriculeSegula'] = 12345 + len(choice)
            user_data['cin'] = 11223344 + len(choice)
            user_data['statusSocial'] = choice
            
            user = User.objects.create_user(
                password='testpass123',
                **user_data
            )
            self.assertEqual(user.statusSocial, choice)
    
    def test_user_sexe_choices(self):
        """Test sexe field choices"""
        valid_choices = ['Homme', 'Femme']
        
        for choice in valid_choices:
            user_data = self.user_data.copy()
            user_data['email'] = f'test_{choice}@segula.com'
            user_data['matriculeSegula'] = 12345 + len(choice)
            user_data['cin'] = 11223344 + len(choice)
            user_data['sexe'] = choice
            
            user = User.objects.create_user(
                password='testpass123',
                **user_data
            )
            self.assertEqual(user.sexe, choice)
    
    def test_user_default_values(self):
        """Test user default values"""
        minimal_user_data = {
            'email': '<EMAIL>',
            'first_name': 'Min',
            'last_name': 'User'
        }
        
        user = User.objects.create_user(
            password='testpass123',
            **minimal_user_data
        )
        
        self.assertEqual(user.soldeConge, 0)
        self.assertEqual(user.soldeAutorisation, 0)
        self.assertFalse(user.teleTravail)
    
    def test_user_str_method(self):
        """Test user string representation"""
        user = User.objects.create_user(
            password='testpass123',
            **self.user_data
        )
        
        expected_str = f"{user.first_name} {user.last_name}"
        self.assertEqual(str(user), expected_str)
    
    def test_user_username_field(self):
        """Test that email is used as username field"""
        self.assertEqual(User.USERNAME_FIELD, 'email')
    
    def test_user_required_fields(self):
        """Test user required fields"""
        # Test that email is required
        with self.assertRaises(ValueError):
            User.objects.create_user(
                email='',
                password='testpass123'
            )
    
    def test_user_site_relationship(self):
        """Test user-site relationship"""
        user = User.objects.create_user(
            password='testpass123',
            **self.user_data
        )
        
        self.assertEqual(user.site, self.site)
        self.assertIn(user, self.site.users.all())
    
    def test_user_role_relationship(self):
        """Test user-role relationship"""
        user = User.objects.create_user(
            password='testpass123',
            **self.user_data
        )
        
        self.assertEqual(user.role, self.role)
        self.assertIn(user, self.role.users.all())


class UserManagerTest(TestCase):
    """Test cases for User manager"""
    
    def test_create_user(self):
        """Test creating a regular user"""
        user = User.objects.create_user(
            email='<EMAIL>',
            password='testpass123',
            first_name='Test',
            last_name='User'
        )
        
        self.assertEqual(user.email, '<EMAIL>')
        self.assertFalse(user.is_staff)
        self.assertFalse(user.is_superuser)
        self.assertTrue(user.check_password('testpass123'))
    
    def test_create_superuser(self):
        """Test creating a superuser"""
        user = User.objects.create_superuser(
            email='<EMAIL>',
            password='adminpass123',
            first_name='Admin',
            last_name='User'
        )
        
        self.assertEqual(user.email, '<EMAIL>')
        self.assertTrue(user.is_staff)
        self.assertTrue(user.is_superuser)
        self.assertTrue(user.check_password('adminpass123'))
