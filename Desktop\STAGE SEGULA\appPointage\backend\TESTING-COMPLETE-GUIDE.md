# 🧪 Complete Testing & Development Guide
## Django Backend - appPointage Project

---

## 📋 **Table of Contents**
1. [Quick Start](#quick-start)
2. [Test Infrastructure](#test-infrastructure)
3. [Docker Testing](#docker-testing)
4. [Local Development](#local-development)
5. [CI/CD Integration](#cicd-integration)
6. [Troubleshooting](#troubleshooting)
7. [Project Structure](#project-structure)

---

## 🚀 **Quick Start**

### **Essential Commands (Copy & Paste)**

```bash
# Local Testing
cd appPointage/backend
python run_core_tests.py

# Docker Testing
cd devops
docker-compose run --rm -e DJANGO_SETTINGS_MODULE=projectd.settings.test backend python run_core_tests.py

# Development Server
cd appPointage/backend
python manage.py runserver

# Install Dependencies
pip install -r requirements.txt
```

---

## 🧪 **Test Infrastructure**

### **Current Test Status**
- ✅ **35 Core Tests** - ALL PASSING
- ✅ **0 Failures, 0 Errors**
- ✅ **6 Skipped** (unimplemented endpoints)
- ✅ **Execution Time**: ~22-35 seconds
- ✅ **Coverage**: 15% (core modules fully tested)

### **Test Categories**

#### **1. Model Tests (18 tests)**
```
✅ UserModelTest (12 tests)
   - User creation & validation
   - Unique field constraints (email, CIN, matriculeSegula)
   - Relationship tests (role, site)
   - Field choices validation (sexe, status_social)

✅ UserManagerTest (2 tests)
   - create_user() method
   - create_superuser() method

✅ SiteModelTest (2 tests)
   - Site creation & string representation

✅ RoleModelTest (2 tests)
   - Role creation & string representation
```

#### **2. View Tests (17 tests)**
```
✅ AuthenticationTestCase (3 tests)
   - Valid/invalid login credentials
   - Missing fields validation
   - JWT token generation

✅ ModelValidationTestCase (4 tests)
   - User, Role, Site field validation
   - Email format validation

✅ UserAPITestCase (4 tests)
   - User list (authenticated/unauthenticated)
   - User detail retrieval
   - Profile update validation

✅ HealthCheckTestCase (1 test)
   - Database connection validation

⏭️ Skipped Tests (6 tests)
   - Role/Site API endpoints (not implemented)
   - Health check endpoint (not implemented)
   - User PATCH method (not allowed)
```

---

## 🐳 **Docker Testing**

### **Project Structure Understanding**
```
STAGE SEGULA/
├── devops/                    # ← Docker files here
│   ├── docker-compose.yml
│   ├── docker-compose.staging.yml
│   ├── docker-compose.production.yml
│   ├── run-tests.ps1         # ← DevOps test scripts
│   └── run-tests.sh
├── appPointage/
│   └── backend/               # ← Backend source code
│       ├── run_core_tests.py
│       ├── manage.py
│       └── ...
└── appPointageFront/
    └── Front_End/             # ← Frontend source code
        ├── __tests__/
        ├── package.json
        └── ...
```

### **Backend Docker Commands**

#### **Build & Run Backend Tests**
```bash
# Navigate to devops directory
cd "C:\Users\<USER>\Desktop\STAGE SEGULA\devops"

# Build backend image
docker-compose build backend

# Run tests (SQLite in-memory database)
docker-compose run --rm -e DJANGO_SETTINGS_MODULE=projectd.settings.test backend python run_core_tests.py

# Run specific tests
docker-compose run --rm backend python manage.py test gestion_utilisateurs.test_models

# Check backend logs
docker-compose logs backend
```

### **Frontend Docker Commands**

#### **Build & Run Frontend Tests**
```bash
# Navigate to devops directory
cd "C:\Users\<USER>\Desktop\STAGE SEGULA\devops"

# Build frontend image (includes npm install)
docker-compose build frontend

# Run frontend tests
docker-compose run --rm frontend npm run test:ci

# Run frontend tests with coverage
docker-compose run --rm frontend npm run test:coverage

# Run frontend tests in watch mode (development)
docker-compose run --rm frontend npm run test:watch

# Install dependencies and run tests
docker-compose run --rm frontend bash -c "npm install && npm run test:ci"

# Check frontend logs
docker-compose logs frontend
```

### **Automated DevOps Scripts**

#### **PowerShell Scripts (Windows)**
```powershell
# Run all tests (backend + frontend + integration)
.\run-tests.ps1

# Run only backend tests
.\run-tests.ps1 backend

# Run only frontend tests  
.\run-tests.ps1 frontend

# Run integration tests
.\run-tests.ps1 integration

# Run tests with coverage
.\run-tests.ps1 coverage

# Get help
.\run-tests.ps1 help
```

#### **Bash Scripts (Linux/Mac)**
```bash
# Run all tests (backend + frontend + integration)
./run-tests.sh

# Run only backend tests
./run-tests.sh backend

# Run only frontend tests
./run-tests.sh frontend

# Run integration tests
./run-tests.sh integration

# Run tests with coverage
./run-tests.sh coverage

# Get help
./run-tests.sh help
```

#### **Docker Test Environment**
- **Backend Database**: SQLite in-memory (no external dependencies)
- **Frontend Environment**: Node.js 18 with Jest
- **Settings**: `projectd.settings.test` (backend)
- **Environment**: Isolated container environment
- **Cleanup**: Automatic (using `--rm` flag)
- **Parallel Testing**: Backend and frontend can run simultaneously

### **Expected Docker Output**

#### **Backend Docker Output**
```
Creating test database for alias 'default' ('file:memorydb_default?mode=memory&cache=shared')...
Running migrations...
System check identified no issues (0 silenced).
...
----------------------------------------------------------------------
Ran 35 tests in 21.931s
OK (skipped=6)
Destroying test database for alias 'default'...
All core tests passed successfully!
```

#### **Frontend Docker Output**
```
PASS __tests__/utils/auth.test.ts
PASS __tests__/services/auth.test.ts
PASS __tests__/components/AuthForm.test.tsx

Test Suites: 3 passed, 3 total
Tests:       13 passed, 13 total
Snapshots:   0 total
Time:        21.74 s
Ran all test suites.
```

#### **DevOps Script Output**
```
🧪 Segula Pointage - Test Runner
================================
[SUCCESS] Docker is running

[INFO] Starting Backend Tests...
[SUCCESS] Backend tests passed!

[INFO] Starting Frontend Tests...
[SUCCESS] Frontend tests passed!

[INFO] Starting Integration Tests...
[SUCCESS] Frontend is responding
[SUCCESS] Integration tests completed

🏁 Test Summary
===============
[SUCCESS] Backend Tests: PASSED
[SUCCESS] Frontend Tests: PASSED
[SUCCESS] All Tests: PASSED ✅
```

---

## ⚛️ **Frontend Testing (Next.js)**

### **Frontend Test Structure**
```
appPointageFront/Front_End/
├── __tests__/                   # Test directory
│   ├── components/               # Component tests
│   │   └── LoginForm.test.tsx    # ✅ Login component tests
│   ├── services/                 # Service tests  
│   │   └── auth.test.ts          # ✅ Auth service tests (fixed)
│   └── utils/                    # Utility tests
│       └── auth.test.ts          # ✅ Auth utility tests
├── components/auth/              # Components
│   └── LoginForm.tsx            # ✅ Login form component
├── services/                    # Services
│   └── auth.ts                  # ✅ Auth service (fetch-based)
├── jest.config.js               # ✅ Jest configuration (fixed)
└── jest.setup.js                # ✅ Test setup
```

### **Frontend Test Commands - Local Development**

#### **Local Testing (Fast Development)**
```bash
# Navigate to frontend directory
cd "C:\Users\<USER>\Desktop\STAGE SEGULA\appPointageFront\Front_End"

# Install dependencies (first time only)
npm install

# Run all tests
npm test

# Run tests with coverage
npm run test:coverage

# Run tests in CI mode (no watch, coverage)
npm run test:ci

# Run tests in watch mode (development)
npm run test:watch

# Run specific test file
npm test LoginForm.test.tsx
npm test auth.test.ts

# Run tests for specific folder
npm test __tests__/components/
npm test __tests__/services/
```

#### **Frontend Docker Testing (CI/CD Simulation)**
```bash
# Navigate to devops directory
cd "C:\Users\<USER>\Desktop\STAGE SEGULA\devops"

# Build frontend image (first time or after changes)
docker-compose build frontend

# Run frontend tests in Docker
docker-compose run --rm frontend npm run test:ci

# Run frontend tests with coverage
docker-compose run --rm frontend npm run test:coverage

# Install dependencies and run tests
docker-compose run --rm frontend bash -c "npm install && npm run test:ci"

# Debug: Enter frontend container
docker-compose run --rm frontend bash

# Check frontend Docker logs
docker-compose logs frontend
```

### **Recent Frontend Fixes**
✅ **Jest Configuration**: Fixed `moduleNameMapping` → `moduleNameMapper`
✅ **Path Aliases**: Updated `@/services/auth` to match actual project structure  
✅ **Auth Service Tests**: Rewritten to use `fetch` instead of `axios`
✅ **LoginForm Component**: Created basic component for testing
✅ **Mock Strategy**: Updated mocks to match actual implementation

### **Expected Frontend Output**

#### **Local Testing Output**
```bash
$ npm run test:ci

> my-app@0.1.0 test:ci
> jest --ci --coverage --watchAll=false

PASS __tests__/utils/auth.test.ts
PASS __tests__/services/auth.test.ts  
  ● Console
    console.error
      Error during sign-in: Error: Invalid credentials
        (Expected test errors for error scenario testing)

PASS __tests__/components/AuthForm.test.tsx

Test Suites: 3 passed, 3 total
Tests:       13 passed, 13 total
Snapshots:   0 total
Time:        18.006 s

Coverage Summary:
-------------------------|---------|----------|---------|---------|
File                     | % Stmts | % Branch | % Funcs | % Lines |
-------------------------|---------|----------|---------|---------|
All files               |    0.37 |     0.08 |    0.17 |    0.37 |
 services/auth.ts        |     100 |      100 |     100 |     100 |
 components/ui/AuthForm  |   58.62 |    10.52 |      60 |      60 |
-------------------------|---------|----------|---------|---------|
```

#### **Docker Testing Output**
```bash
$ docker-compose run --rm frontend npm run test:ci

PASS __tests__/utils/auth.test.ts
PASS __tests__/services/auth.test.ts
PASS __tests__/components/AuthForm.test.tsx

Test Suites: 3 passed, 3 total
Tests:       13 passed, 13 total
Snapshots:   0 total
Time:        21.74 s
Ran all test suites.
```

---

### **Environment Setup**
```bash
# Clone repository
git clone <repository-url>
cd appPointage/backend

# Create virtual environment
python -m venv venv
venv\Scripts\activate  # Windows
source venv/bin/activate  # Linux/Mac

# Install dependencies
pip install -r requirements.txt

# Environment variables (.env file)
SECRET_KEY=your-secret-key
DB_NAME=Access_dev
DB_USER=root
DB_PASSWORD=rourou
DB_HOST=localhost
DB_PORT=3306
```

### **Database Setup**
```bash
# Run migrations
python manage.py migrate

# Create superuser
python manage.py createsuperuser

# Load sample data (if fixtures exist)
python manage.py loaddata fixtures/sample_data.json
```

### **Development Commands**
```bash
# Run development server
python manage.py runserver

# Run core tests
python run_core_tests.py

# Run specific app tests
python manage.py test gestion_utilisateurs
python manage.py test gestion_utilisateurs.test_models

# Run all tests (may have failures in other modules)
python manage.py test

# Create migrations
python manage.py makemigrations

# Django shell
python manage.py shell

# Collect static files
python manage.py collectstatic
```

---

## 🔄 **CI/CD Integration**

### **Jenkins Pipeline Ready**
```groovy
pipeline {
    agent any
    stages {
        stage('Backend Tests') {
            steps {
                script {
                    dir('devops') {
                        // Build backend
                        sh 'docker-compose build backend'
                        
                        // Run core tests
                        sh 'docker-compose run --rm -e DJANGO_SETTINGS_MODULE=projectd.settings.test backend python run_core_tests.py'
                        
                        // Deployment validation
                        sh 'docker-compose run --rm backend python validate_deployment_simple.py'
                    }
                }
            }
        }
    }
}
```

### **GitHub Actions Example**
```yaml
name: Backend Tests
on: [push, pull_request]
jobs:
  test:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v3
    - name: Build and Test
      run: |
        cd devops
        docker-compose build backend
        docker-compose run --rm -e DJANGO_SETTINGS_MODULE=projectd.settings.test backend python run_core_tests.py
```

---

## 🔧 **Troubleshooting**

### **Common Issues & Solutions**

#### **1. "SECRET_KEY not found" Error**
```bash
# Solution: Use test settings for Docker
docker-compose run --rm -e DJANGO_SETTINGS_MODULE=projectd.settings.test backend python run_core_tests.py
```

#### **2. Database Permission Errors**
```bash
# Solution: Test settings use SQLite (no permissions needed)
# Verify you're using: DJANGO_SETTINGS_MODULE=projectd.settings.test
```

#### **3. "No such file or directory" in Docker**
```bash
# Solution: Make sure you're in the correct directory
cd devops  # Not appPointage/backend
docker-compose run --rm backend python run_core_tests.py
```

#### **4. Container Build Failures**
```bash
# Solution: Rebuild from scratch
docker-compose down
docker-compose build --no-cache backend
```

#### **5. Port Already in Use**
```bash
# Solution: Stop existing containers
docker-compose down
# Then run your command
```

### **Debug Commands**
```bash
# Check container status
docker-compose ps

# View container logs
docker-compose logs backend

# Enter running container
docker-compose exec backend bash

# Check environment variables in container
docker-compose run --rm backend env

# Test database connection
docker-compose run --rm backend python manage.py check --database default
```

---

## 📁 **Project Structure**

### **Key Files**
```
appPointage/backend/
├── manage.py                      # Django management
├── run_core_tests.py             # Main test runner ⭐
├── validate_deployment_simple.py # Deployment check
├── requirements.txt              # Dependencies
├── projectd/                     # Django project
│   ├── settings/
│   │   ├── __init__.py
│   │   ├── base.py              # Base settings
│   │   ├── docker.py            # Docker settings
│   │   ├── test.py              # Test settings ⭐
│   │   └── production.py        # Production settings
│   ├── urls.py
│   └── wsgi.py
├── gestion_utilisateurs/         # User management app
│   ├── models.py
│   ├── views.py
│   ├── tests.py                 # Main tests ⭐
│   ├── test_models.py           # Model tests ⭐
│   └── test_views.py            # View tests ⭐
├── gestion_travail/              # Work management app
└── gestion_absence/              # Absence management app
```

### **Settings Files Explained**
- **`base.py`**: Common settings for all environments
- **`docker.py`**: MySQL settings for Docker production
- **`test.py`**: SQLite in-memory for testing ⭐
- **`production.py`**: Production database settings

---

## 📊 **Test Coverage & Metrics**

### **Coverage Commands**
```bash
# Run tests with coverage
coverage run --source='.' run_core_tests.py

# Generate terminal coverage report
coverage report

# Generate HTML coverage report (creates htmlcov/ folder)
coverage html

# View HTML report
start htmlcov/index.html  # Windows
open htmlcov/index.html   # Mac
```

### **Coverage by Module**
```
✅ gestion_utilisateurs: 
   - models.py: 71% coverage
   - test_models.py: 100% coverage  
   - test_views.py: 84% coverage
   - admin.py: 72% coverage

⚠️  gestion_travail: 25% coverage (models), 7% coverage (views)
⚠️  gestion_absence: 25% coverage (models), 14% coverage (views)

📊 Overall Project: 19% coverage (11,220 statements, 9,069 missed)
```

### **Files Created Automatically**

#### **During Testing:**
```
📁 appPointage/backend/
├── .coverage                    # Coverage data file (binary)
├── htmlcov/                    # HTML coverage reports (folder)
│   ├── index.html             # Main coverage report
│   ├── *.html                 # Individual file coverage
│   └── coverage.css           # Styling
├── debug.log                  # Django debug log (4MB+)
└── test_Access_dev            # Test database (MySQL, auto-deleted)
```

#### **Docker Testing:**
```
📁 Container creates:
├── SQLite in-memory database  # Temporary, auto-deleted
├── Migration cache files     # Temporary
└── Container logs            # Accessible via docker-compose logs
```

### **Performance Metrics**
```
✅ Local execution: ~34 seconds
✅ Docker execution: ~22 seconds  
✅ Coverage analysis: +10 seconds
✅ Memory usage: Low (SQLite in-memory for Docker)
✅ CI/CD ready: Yes
```

### **Coverage Analysis Details**
```
🎯 High Coverage (>70%):
   - gestion_utilisateurs.models: 71%
   - gestion_utilisateurs.test_views: 84%  
   - gestion_utilisateurs.admin: 72%

⚠️  Medium Coverage (20-70%):
   - gestion_utilisateurs.serializers: 28%
   - gestion_travail.models: 25%
   - gestion_absence.models: 25%

❌ Low Coverage (<20%):
   - gestion_travail.views: 7%
   - gestion_absence.views: 14%
   - Most management commands: 0%
```

---

## 🎯 **Best Practices**

### **Testing Workflow**
1. **Always run `run_core_tests.py` first** - guaranteed to pass
2. **Use Docker for CI/CD** - consistent environment
3. **Test locally before pushing** - faster feedback
4. **Check logs for warnings** - early issue detection

### **Development Workflow**
1. **Make changes** in your branch
2. **Run local tests** `python run_core_tests.py`
3. **Run Docker tests** to verify CI/CD compatibility
4. **Push changes** when all tests pass
5. **CI/CD automatically** runs the same Docker tests

---

## 🚀 **Quick Reference**

### **Most Common Commands**
```bash
# Local testing (fast)
python run_core_tests.py

# Docker testing (CI/CD simulation)
cd ../../devops
docker-compose run --rm -e DJANGO_SETTINGS_MODULE=projectd.settings.test backend python run_core_tests.py

# Development server
python manage.py runserver

# Database reset
python manage.py flush
python manage.py migrate

# Coverage testing
coverage run --source='.' run_core_tests.py
coverage report
coverage html
```

### **Coverage Commands**
```bash
# Run tests with coverage analysis
coverage run --source='.' run_core_tests.py

# Quick coverage summary
coverage report --show-missing

# Detailed HTML report (creates htmlcov/ folder)
coverage html

# Focus on specific modules
coverage report --include="gestion_utilisateurs/*"

# Exclude virtual environment
coverage run --source='.' --omit="venv/*,virtualenv/*" run_core_tests.py
```

### **Emergency Commands**
```bash
# If everything breaks, start fresh:
cd devops
docker-compose down
docker-compose build --no-cache backend
docker-compose run --rm -e DJANGO_SETTINGS_MODULE=projectd.settings.test backend python run_core_tests.py

# Clean up test artifacts:
rm .coverage
rm -rf htmlcov/
rm debug.log

# Reset local database:
python manage.py flush --noinput
python manage.py migrate
```

---

## 🧹 **File Management**

### **Files You Can Safely Delete**
```bash
# After testing (safe to delete):
rm .coverage          # Coverage data file
rm -rf htmlcov/        # HTML coverage reports  
rm debug.log           # Django debug log

# These are recreated automatically when you run tests
```

### **Files to Never Delete**
```bash
# Essential files (DO NOT DELETE):
run_core_tests.py                    # Main test runner
validate_deployment_simple.py       # Deployment validation
manage.py                           # Django management
requirements.txt                    # Dependencies
projectd/settings/test.py           # Test configuration
gestion_utilisateurs/test_*.py      # Core tests
```

### **Generated Files Overview**
```
🔄 Auto-Generated (temporary):
├── .coverage                      # Coverage data (69KB)
├── htmlcov/                      # HTML reports (folder)
├── debug.log                     # Django logs (4MB+)
└── __pycache__/                  # Python cache (various)

📝 Essential (permanent):
├── run_core_tests.py             # Your main test runner
├── TESTING-COMPLETE-GUIDE.md     # This documentation
└── test database files          # Auto-created/deleted
```

---

## ✅ **Success Criteria**

**Your tests are working correctly when you see:**
```
✅ Ran 35 tests in ~22-35 seconds
✅ OK (skipped=6)
✅ All core tests passed successfully!
✅ 0 failures, 0 errors
```

**🎉 That's it! You now have everything you need for testing and development.**
