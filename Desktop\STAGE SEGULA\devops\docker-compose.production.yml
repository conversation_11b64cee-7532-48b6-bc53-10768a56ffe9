# Production overrides for docker-compose.yml
version: '3.8'

services:
  db:
    restart: always
    volumes:
      - prod_db_data:/var/lib/mysql
    environment:
      MYSQL_ROOT_PASSWORD: ${DB_ROOT_PASSWORD}
      MYSQL_DATABASE: ${DB_NAME}
      MYSQL_USER: ${DB_USER}
      MYSQL_PASSWORD: ${DB_PASSWORD}

  backend:
    restart: always
    environment:
      - DJANGO_DEBUG=false
      - DJANGO_ALLOWED_HOSTS=${DJANGO_ALLOWED_HOSTS}
    ports:
      - "8000:8000"  # Direct access to backend
    volumes:
      - static_files:/app/static
      - media_files:/app/media
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '0.5'

  frontend:
    restart: always
    environment:
      - NODE_ENV=production
      - NEXT_PUBLIC_API_URL=http://localhost:8000
    ports:
      - "3000:3000"  # Direct access to frontend
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.3'

volumes:
  prod_db_data:
  static_files:
  media_files:
