# Docker Environment Variables for Testing
# This file provides default values for testing in Docker containers

# Django Settings
SECRET_KEY=django-insecure-test-key-for-docker-only
DJANGO_SETTINGS_MODULE=projectd.settings.docker

# Database Configuration
DB_NAME=Access_dev
DB_USER=root
DB_PASSWORD=rourou
DB_HOST=localhost
DB_PORT=3306

# Email Configuration (optional for testing)
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=test-password

# Docker Flag
RUNNING_IN_DOCKER=true

# Frontend URL
FRONTEND_URL=http://localhost:3000
NEXT_PUBLIC_API_URL=http://localhost:8000
