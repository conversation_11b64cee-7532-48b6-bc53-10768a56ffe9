version: '3.8'

services:
  db:
    image: mariadb:10.11
    environment:
      MYSQL_ROOT_PASSWORD: ${DB_ROOT_PASSWORD}
      MYSQL_DATABASE: ${DB_NAME}
      MYSQL_USER: ${DB_USER}
      MYSQL_PASSWORD: ${DB_PASSWORD}
    healthcheck:
      test: ["C<PERSON>", "mysqladmin", "ping", "-u", "root", "-p${DB_ROOT_PASSWORD}", "-h", "localhost"]
      interval: 10s
      timeout: 5s
      retries: 5
    ports:
      - "${DB_PORT_HOST}:3306"
    volumes:
      - db_data:/var/lib/mysql
    networks:
      - app_network

  backend:
    build: 
      context: ${BACKEND_REPO_PATH}
      dockerfile: Dockerfile
    environment:
      - RUNNING_IN_DOCKER=${RUNNING_IN_DOCKER}
      - DB_NAME=${DB_NAME}
      - DB_USER=${DB_USER}
      - DB_PASSWORD=${DB_PASSWORD}
      - DB_HOST=${DB_HOST}
      - DB_PORT=${DB_PORT}
      - SECRET_KEY=${SECRET_KEY}
      - EMAIL_HOST_USER=${EMAIL_HOST_USER}
      - EMAIL_HOST_PASSWORD=${EMAIL_HOST_PASSWORD}
    ports:
      - "${BACKEND_PORT}:8000"
    depends_on:
      db:
        condition: service_healthy
    networks:
      - app_network

  frontend:
    build: 
      context: ${FRONTEND_REPO_PATH}
      dockerfile: Dockerfile
    environment:
      - NEXT_PUBLIC_API_URL=${NEXT_PUBLIC_API_URL}
    ports:
      - "${FRONTEND_PORT}:3000"
    depends_on:
      - backend
    networks:
      - app_network

volumes:
  db_data:

networks:
  app_network:
    driver: bridge
