pipeline {
    agent any

    environment {
        BACKEND_REPO = 'https://github.com/softwareDevSegula/appPointage.git'
        FRONTEND_REPO = 'https://github.com/softwareDevSegula/appPointageFront.git'
    }

    stages {
        stage('Checkout') {
            steps {
                echo 'Checking out repositories...'

                // Checkout DevOps repo (current)
                checkout scm

                // Checkout Backend repo
                dir('backend') {
                    git branch: 'main', url: "${BACKEND_REPO}", credentialsId: 'github-pat'
                }

                // Checkout Frontend repo
                dir('frontend') {
                    git branch: 'main', url: "${FRONTEND_REPO}", credentialsId: 'github-pat'
                }
            }
        }

        stage('Setup') {
            steps {
                echo 'Setting up environment...'

                // Copy production environment file
                sh 'cp .env.production .env'

                // Update repository paths for CI/CD
                sh '''
                    sed -i 's|BACKEND_REPO_PATH=.*|BACKEND_REPO_PATH=./backend|g' .env
                    sed -i 's|FRONTEND_REPO_PATH=.*|FRONTEND_REPO_PATH=./frontend|g' .env
                '''
            }
        }

        stage('Build') {
            steps {
                echo 'Building Docker images...'
                sh 'docker-compose build'
            }
        }

        stage('Deploy') {
            when {
                anyOf {
                    branch 'main'
                    branch 'develop'
                }
            }
            steps {
                echo 'Deploying application...'

                script {
                    if (env.BRANCH_NAME == 'main') {
                        // Production deployment
                        sh 'docker-compose -f docker-compose.yml -f docker-compose.production.yml up -d'
                    } else {
                        // Staging deployment
                        sh 'docker-compose up -d'
                    }
                }
            }
        }

        stage('Health Check') {
            steps {
                echo 'Checking application health...'

                // Wait for services to start
                sh 'sleep 30'

                // Basic health checks
                sh '''
                    echo "Checking services..."
                    docker-compose ps

                    echo "Checking backend..."
                    curl -f http://localhost:8000/login/ || echo "Backend check failed"

                    echo "Checking frontend..."
                    curl -f http://localhost:3000 || echo "Frontend check failed"
                '''
            }
        }
    }

    post {
        always {
            echo 'Cleaning up...'
            sh 'docker-compose down || true'
        }

        success {
            echo 'Pipeline completed successfully!'
        }

        failure {
            echo 'Pipeline failed!'
        }
    }
}
