pipeline {
    agent any

    environment {
        // Repository URLs
        BACKEND_REPO = 'https://github.com/softwareDevSegula/appPointage.git'
        FRONTEND_REPO = 'https://github.com/softwareDevSegula/appPointageFront.git'

        // Environment variables from Jenkins credentials/parameters
        DB_NAME = credentials('db-name')
        DB_USER = credentials('db-user')
        DB_PASSWORD = credentials('db-password')
        DB_HOST = 'db'
        DB_PORT = '3306'
        DB_ROOT_PASSWORD = credentials('db-root-password')
        SECRET_KEY = credentials('django-secret-key')
        NEXT_PUBLIC_API_URL = 'http://backend:8000'
    }

    stages {
        stage('Checkout') {
            steps {
                echo 'Checking out repositories...'

                // Checkout DevOps repo (current)
                checkout scm

                // Checkout Backend repo
                dir('backend') {
                    git branch: 'main', url: "${BACKEND_REPO}", credentialsId: 'segula-pat'
                }

                // Checkout Frontend repo
                dir('frontend') {
                    git branch: 'main', url: "${FRONTEND_REPO}", credentialsId: 'segula-pat'
                }
            }
        }

        stage('Setup') {
            steps {
                echo 'Setting up environment...'

                // Create .env file from Jenkins environment variables
                sh '''
                    cat > .env << EOF
# Repository paths for CI/CD
BACKEND_REPO_PATH=./backend
FRONTEND_REPO_PATH=./frontend

# Database configuration
DB_NAME=${DB_NAME}
DB_USER=${DB_USER}
DB_PASSWORD=${DB_PASSWORD}
DB_HOST=${DB_HOST}
DB_PORT=${DB_PORT}
DB_ROOT_PASSWORD=${DB_ROOT_PASSWORD}

# Django configuration
SECRET_KEY=${DJANGO_SECRET_KEY}

# Frontend configuration
NEXT_PUBLIC_API_URL=${NEXT_PUBLIC_API_URL}

EOF
                '''

                echo 'Environment file created successfully'
            }
        }

        stage('Build') {
            steps {
                echo 'Building Docker images...'
                sh 'docker-compose build'
            }
        }

        stage('Deploy') {
            when {
                anyOf {
                    branch 'main'
                    branch 'develop'
                }
            }
            steps {
                echo 'Deploying application...'

                script {
                    if (env.BRANCH_NAME == 'main') {
                        // Production deployment
                        sh 'docker-compose -f docker-compose.yml -f docker-compose.production.yml up -d'
                    } else {
                        // Staging deployment
                        sh 'docker-compose up -d'
                    }
                }
            }
        }

        stage('Health Check') {
            steps {
                echo 'Checking application health...'

                // Wait for services to start
                sh 'sleep 30'

                // Basic health checks
                sh '''
                    echo "Checking services..."
                    docker-compose ps

                    echo "Checking backend..."
                    curl -f http://localhost:8000/login/ || echo "Backend check failed"

                    echo "Checking frontend..."
                    curl -f http://localhost:3000 || echo "Frontend check failed"
                '''
            }
        }
    }

    post {
        always {
            echo 'Cleaning up...'
            sh 'docker-compose down || true'
        }

        success {
            echo 'Pipeline completed successfully!'
        }

        failure {
            echo 'Pipeline failed!'
        }
    }
}
