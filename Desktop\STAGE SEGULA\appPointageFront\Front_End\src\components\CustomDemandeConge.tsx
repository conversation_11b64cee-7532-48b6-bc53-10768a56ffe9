'use client';

import { useState, useEffect } from "react";
import { Calendar } from "@/src/components/ui/calendar";
import { Popover, PopoverContent, PopoverTrigger } from "@/src/components/ui/popover";
import { Button } from "@/src/components/ui/button";
import { Input } from "@/src/components/ui/input";
import { Calendar as CalendarIcon } from "lucide-react";
import { format } from "date-fns";
import { cn } from "@/lib/utils";
import { toast } from "@/src/components/ui/use-toast";
import { demanderConge, getSoldeConge } from "@/services/conge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/src/components/ui/select";
import { jwtDecode } from "jwt-decode";
import Cookies from "js-cookie";

// Types de congés disponibles
const congeTypes = [
  "MALADIE",
  "CONGE PAYE",
  "CONGE SANS SOLDE",
  "CONGE DEMI JOURNEE",
  "DEPLACEMENT",
  "MISSION",
  "MATERINITE",
  "PATERNITE"
];

// Types d'alertes disponibles
const alertTypes = [
  "URGENT",
  "NORMAL",
  "NON PRIORITAIRE"
];

const CustomDemandeConge: React.FC = () => {
  const [startDate, setStartDate] = useState<Date | undefined>();
  const [endDate, setEndDate] = useState<Date | undefined>();
  const [motifConge, setMotifConge] = useState<string>("");
  const [adresse, setAdresse] = useState<string>("");
  const [telephone, setTelephone] = useState<string>("");
  const [isLoading, setIsLoading] = useState<boolean>(false);
  const [typeAlert, setTypeAlert] = useState<string>("");
  const [matriculeSegula, setMatriculeSegula] = useState<string>("");
  const [solde, setSolde] = useState<number>(0);
  const [bu, setBu] = useState<string>("");
  const [teamManager, setTeamManager] = useState<string>("");
  const [isTypeCongeOpen, setIsTypeCongeOpen] = useState(false);
  const [isTypeAlerteOpen, setIsTypeAlerteOpen] = useState(false);
  const [demiJourneeType, setDemiJourneeType] = useState<string>("");
  const [demiJourneeDate, setDemiJourneeDate] = useState<Date | undefined>();
  
  // États pour les erreurs de validation
  const [errors, setErrors] = useState<Record<string, boolean>>({
    startDate: false,
    endDate: false,
    motifConge: false,
    adresse: false,
    telephone: false
  });

  useEffect(() => {
    const token = Cookies.get("access_token");
    if (token) {
      try {
        const decodedToken: any = jwtDecode(token);
        setMatriculeSegula(decodedToken.matriculeSegula || decodedToken.user_id?.toString());
        setBu(localStorage.getItem("bu") || "");
        setTeamManager(localStorage.getItem("teamManager") || "");
      } catch (error) {
        console.error("Erreur lors du décodage du token:", error);
        toast({
          title: "Erreur",
          description: "Impossible de récupérer les informations utilisateur",
          variant: "destructive",
        });
      }
    }
  }, []);

  useEffect(() => {
    const fetchSolde = async () => {
      try {
        console.log("Matricule utilisé :", matriculeSegula);
        const data = await getSoldeConge(matriculeSegula);
        console.log("Réponse API :", data);
        if (typeof data === "number") {
          console.log("Solde récupéré :", data);
          setSolde(data);
        } else if (data && data.solde_restant !== undefined) {
          console.log("Solde récupéré :", data.solde_restant);
          setSolde(data.solde_restant);
        } else {
          console.warn("Donnée incorrecte :", data);
        }
      } catch (error) {
        console.error("Erreur lors de la récupération du solde :", error);
      }
    };
  
    if (matriculeSegula) {
      fetchSolde();
    }
  }, [matriculeSegula]);

  const validateFields = () => {
    let newErrors;
    if (motifConge === "CONGE DEMI JOURNEE") {
      newErrors = {
        demiJourneeType: !demiJourneeType,
        demiJourneeDate: !demiJourneeDate,
        motifConge: !motifConge || motifConge.trim() === "",
        adresse: !adresse || adresse.trim() === "",
        telephone: !telephone || telephone.trim() === ""
      };
    } else {
      newErrors = {
        startDate: !startDate,
        endDate: !endDate,
        motifConge: !motifConge || motifConge.trim() === "",
        adresse: !adresse || adresse.trim() === "",
        telephone: !telephone || telephone.trim() === ""
      };
    }
    setErrors(newErrors);
    return !Object.values(newErrors).some(error => error);
  };

  const handleSubmit = async () => {
    if (!validateFields()) {
      toast({
        title: "Champs manquants",
        description: "Veuillez remplir tous les champs obligatoires",
        variant: "destructive"
      });
      return;
    }

    // Validation des dates
    if (motifConge !== "CONGE DEMI JOURNEE" && startDate && endDate && startDate > endDate) {
      toast({
        title: "Erreur de dates",
        description: "La date de fin doit être postérieure à la date de début",
        variant: "destructive",
      });
      return;
    }

    setIsLoading(true);

    try {
      let requestData;
      if (motifConge === "CONGE DEMI JOURNEE") {
        requestData = {
          matriculeSegula: matriculeSegula,
          date_debut: demiJourneeDate ? format(demiJourneeDate, "yyyy-MM-dd") : "",
          date_fin: demiJourneeDate ? format(demiJourneeDate, "yyyy-MM-dd") : "",
          motif_conge: `${motifConge} - ${demiJourneeType}`,
          adresse: adresse.trim(),
          telephone: telephone.trim(),
          type_alert: typeAlert,
          solde: solde,
          bu: bu,
          team_manager: teamManager,
        };
      } else {
        requestData = {
          matriculeSegula: matriculeSegula,
          date_debut: startDate ? format(startDate, "yyyy-MM-dd") : "",
          date_fin: endDate ? format(endDate, "yyyy-MM-dd") : "",
          motif_conge: motifConge.trim(),
          adresse: adresse.trim(),
          telephone: telephone.trim(),
          type_alert: typeAlert,
          solde: solde,
          bu: bu,
          team_manager: teamManager,
        };
      }
      console.log("Données à envoyer:", requestData);
      const response = await demanderConge(requestData);
      if (response.error) {
        toast({
          title: "Erreur",
          description: response.error,
          variant: "destructive",
        });
      } else {
        toast({
          title: "Succès",
          description: "Demande de congé envoyée avec succès.",
          variant: "success",
          className: "bg-green-100 text-green-800",
        });
        // Réinitialiser le formulaire
        setStartDate(undefined);
        setEndDate(undefined);
        setDemiJourneeType("");
        setDemiJourneeDate(undefined);
        setMotifConge("");
        setAdresse("");
        setTelephone("");
        setTypeAlert("");
        setErrors({
          startDate: false,
          endDate: false,
          motifConge: false,
          adresse: false,
          telephone: false,
          demiJourneeType: false,
          demiJourneeDate: false
        });
      }
    } catch (error) {
      toast({
        title: "Erreur serveur",
        description: "Une erreur s'est produite lors de l'envoi de la demande.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="w-full max-w-2xl mx-auto p-6">
      <div className="grid grid-cols-2 gap-x-8 gap-y-4">
        {/* Type Congé */}
        <div>
          <label className="block text-gray-600 mb-1">Type Congé *</label>
          <Select 
            onValueChange={(value) => {
              setMotifConge(value);
              setErrors({...errors, motifConge: false});
            }} 
            value={motifConge}
            onOpenChange={(open) => setIsTypeCongeOpen(open)}
          >
            <SelectTrigger className={cn(
              "w-full bg-gray-100 border-gray-300 hover:bg-gray-200",
              errors.motifConge && "border-red-500",
              isTypeCongeOpen && "bg-blue-50 border-blue-500 ring-2 ring-blue-200"
            )}>
              <SelectValue placeholder="Sélectionnez un type de congé" />
            </SelectTrigger>
            <SelectContent className="bg-gray-100 border-gray-300">
              {congeTypes.map((type, index) => (
                <SelectItem 
                  key={index} 
                  value={type}
                  className="hover:bg-blue-100 focus:bg-blue-100"
                >
                  {type}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          {errors.motifConge && (
            <p className="mt-1 text-sm text-red-600">Ce champ est obligatoire</p>
          )}
        </div>

        {/* Solde */}
        <div>
          <label className="block text-gray-600 mb-1">Solde</label>
          <Input
            type="text"
            value={solde}
            disabled
            readOnly
            className="w-full bg-gray-100 border-gray-300 text-gray-500 cursor-not-allowed"
          />
          <span className="text-sm text-gray-500 mt-1">Solde en jours</span>
        </div>

        {/* Type d'alerte */}
        <div className="col-span-2">
          <label className="block text-gray-600 mb-1">Type d&apos;alerte</label>
          <Select 
            onValueChange={setTypeAlert} 
            value={typeAlert}
            onOpenChange={(open) => setIsTypeAlerteOpen(open)}
          >
            <SelectTrigger className={cn(
              "w-full bg-gray-100 border-gray-300 hover:bg-gray-200",
              isTypeAlerteOpen && "bg-blue-50 border-blue-500 ring-2 ring-blue-200"
            )}>
              <SelectValue placeholder="Sélectionnez un niveau d'urgence" />
            </SelectTrigger>
            <SelectContent className="bg-gray-100 border-gray-300">
              {alertTypes.map((type, index) => (
                <SelectItem 
                  key={index} 
                  value={type}
                  className="hover:bg-blue-100 focus:bg-blue-100"
                >
                  {type}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Date début/fin OU demi-journée */}
        {motifConge === "CONGE DEMI JOURNEE" ? (
          <>
            <div>
              <label className="block text-gray-600 mb-1">Demi-journée *</label>
              <Select
                onValueChange={(value) => {
                  setDemiJourneeType(value);
                  setErrors({ ...errors, demiJourneeType: false });
                }}
                value={demiJourneeType}
              >
                <SelectTrigger className={cn(
                  "w-full bg-gray-100 border-gray-300 hover:bg-gray-200",
                  errors.demiJourneeType && "border-red-500"
                )}>
                  <SelectValue placeholder="Sélectionnez la période" />
                </SelectTrigger>
                <SelectContent className="bg-gray-100 border-gray-300">
                  <SelectItem value="Matinée">Matinée</SelectItem>
                  <SelectItem value="Après-midi">Après-midi</SelectItem>
                </SelectContent>
              </Select>
              {errors.demiJourneeType && (
                <p className="mt-1 text-sm text-red-600">Ce champ est obligatoire</p>
              )}
            </div>
            <div>
              <label className="block text-gray-600 mb-1">Date de la demi-journée *</label>
              <Input
                type="date"
                value={demiJourneeDate ? format(demiJourneeDate, "yyyy-MM-dd") : ""}
                onChange={(e) => {
                  setDemiJourneeDate(e.target.valueAsDate || undefined);
                  setErrors({ ...errors, demiJourneeDate: false });
                }}
                className={cn(
                  "w-full border-gray-300",
                  errors.demiJourneeDate && "border-red-500"
                )}
              />
              {errors.demiJourneeDate && (
                <p className="mt-1 text-sm text-red-600">Ce champ est obligatoire</p>
              )}
            </div>
          </>
        ) : (
          <>
            {/* Date début */}
            <div>
              <label className="block text-gray-600 mb-1">Date début *</label>
              <Input
                type="date"
                value={startDate ? format(startDate, "yyyy-MM-dd") : ""}
                onChange={(e) => {
                  setStartDate(e.target.valueAsDate || undefined);
                  setErrors({ ...errors, startDate: false });
                }}
                className={cn(
                  "w-full border-gray-300",
                  errors.startDate && "border-red-500"
                )}
              />
              {errors.startDate && (
                <p className="mt-1 text-sm text-red-600">Ce champ est obligatoire</p>
              )}
            </div>
            {/* Date fin */}
            <div>
              <label className="block text-gray-600 mb-1">Date fin *</label>
              <Input
                type="date"
                value={endDate ? format(endDate, "yyyy-MM-dd") : ""}
                onChange={(e) => {
                  setEndDate(e.target.valueAsDate || undefined);
                  setErrors({ ...errors, endDate: false });
                }}
                className={cn(
                  "w-full border-gray-300",
                  errors.endDate && "border-red-500"
                )}
              />
              {errors.endDate && (
                <p className="mt-1 text-sm text-red-600">Ce champ est obligatoire</p>
              )}
            </div>
          </>
        )}

        {/* Contact */}
        <div>
          <label className="block text-gray-600 mb-1">Contact pendant le congé *</label>
          <Input
            type="tel"
            value={telephone}
            onChange={(e) => {
              setTelephone(e.target.value);
              setErrors({...errors, telephone: false});
            }}
            placeholder="Numéro de téléphone"
            className={cn(
              "w-full border-gray-300",
              errors.telephone && "border-red-500"
            )}
          />
          {errors.telephone && (
            <p className="mt-1 text-sm text-red-600">Ce champ est obligatoire</p>
          )}
        </div>

        {/* Adresse */}
        <div>
          <label className="block text-gray-600 mb-1">Adresse pendant le congé *</label>
          <Input
            type="text"
            value={adresse}
            onChange={(e) => {
              setAdresse(e.target.value);
              setErrors({...errors, adresse: false});
            }}
            placeholder="Adresse"
            className={cn(
              "w-full border-gray-300",
              errors.adresse && "border-red-500"
            )}
          />
          {errors.adresse && (
            <p className="mt-1 text-sm text-red-600">Ce champ est obligatoire</p>
          )}
        </div>

        {/* Bouton de soumission */}
        <div className="col-span-2 mt-4">
          <Button
            onClick={handleSubmit}
            disabled={isLoading}
            className="w-full bg-blue-600 hover:bg-blue-700 text-white"
          >
            {isLoading ? "Envoi en cours..." : "Envoyer la demande"}
          </Button>
        </div>
      </div>
    </div>
  );
};

export default CustomDemandeConge;