from rest_framework.response import Response
from rest_framework.decorators import api_view, permission_classes
from rest_framework import status
from rest_framework.permissions import IsAuthenticated
from django.core.exceptions import ObjectDoesNotExist
from gestion_utilisateurs.models import <PERSON>ivite, <PERSON><PERSON><PERSON>, <PERSON><PERSON>bor<PERSON><PERSON>, <PERSON><PERSON>
from gestion_utilisateurs.models import <PERSON>iv<PERSON>, <PERSON><PERSON><PERSON>, <PERSON>laborateur, <PERSON><PERSON>
from gestion_utilisateurs.serializers import ActiviteSerializer, UserSerializer
from gestion_utilisateurs.models import User, DirecteurDePole
from gestion_utilisateurs.decorator import role_required
from gestion_utilisateurs.models import TeamManager
from django.shortcuts import get_object_or_404

@api_view(['POST'])
# @permission_classes([IsAuthenticated])
def creer_activite(request):
    if request.method == 'POST':
        nom_activite = request.data.get('nom_activite')
        equipe_id = request.data.get('equipe_id')

        if not nom_activite:
            return Response({"message": "Le nom de l'activité est requis"}, status=status.HTTP_400_BAD_REQUEST)

        if Activite.objects.filter(nom_activite__iexact=nom_activite).exists():
            return Response({"message": "Une activité avec ce nom existe déjà"}, status=status.HTTP_400_BAD_REQUEST)

        try:
            equipe = Equipe.objects.get(id=equipe_id) if equipe_id else None

            activite = Activite.objects.create(
                nom_activite=nom_activite,
                equipe=equipe
            )
            serializer = ActiviteSerializer(activite)
            return Response(serializer.data, status=status.HTTP_201_CREATED)
        except Equipe.DoesNotExist:
            return Response({"message": "Equipe introuvable avec cet ID"}, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            return Response({"message": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['PUT'])
@permission_classes([IsAuthenticated])
def modifier_activite(request):
    try:
        nom_activite = request.data.get('nom_activite')
        if not nom_activite:
            return Response({"message": "Le nom actuel de l'activité est requis pour l'identification"}, status=status.HTTP_400_BAD_REQUEST)

        activite = Activite.objects.get(nom_activite=nom_activite)
        
        new_nom_activite = request.data.get('new_nom_activite', nom_activite)
        equipe_id = request.data.get('equipe_id')

        # Validate the new name
        if new_nom_activite != nom_activite:
            if Activite.objects.filter(nom_activite__iexact=new_nom_activite).exists():
                return Response({"message": "Une activité avec ce nom existe déjà"}, status=status.HTTP_400_BAD_REQUEST)

        try:
            # Update the name
            activite.nom_activite = new_nom_activite
            
            # Update the team if provided
            if equipe_id is not None:
                if equipe_id:
                    activite.equipe = Equipe.objects.get(id=equipe_id)
                else:
                    activite.equipe = None
                    
            activite.save()
            serializer = ActiviteSerializer(activite)
            return Response(serializer.data, status=status.HTTP_200_OK)
        except Equipe.DoesNotExist:
            return Response({"message": "Equipe introuvable avec cet ID"}, status=status.HTTP_400_BAD_REQUEST)
    except Activite.DoesNotExist:
        return Response({"message": f"Activité '{nom_activite}' introuvable"}, status=status.HTTP_404_NOT_FOUND)
    except Exception as e:
        return Response({"message": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['DELETE'])
@permission_classes([IsAuthenticated])
@role_required("Rh business unit", "Directeur Pays", "Directeur Rh Pays", "Business Unit Manager")
def supprimer_activite(request):
    try:
        nom_activite = request.data.get('nom_activite') or request.query_params.get('nom_activite')
        if not nom_activite:
            return Response(
                {"message": "Le nom de l'activité est requis"},
                status=status.HTTP_400_BAD_REQUEST
            )
        activite = Activite.objects.get(nom_activite=nom_activite)
        activite.delete()
        return Response(
            {"message": f"Activité '{nom_activite}' supprimée avec succès"},
            status=status.HTTP_200_OK
        )
    except Activite.DoesNotExist:
        return Response(
            {"message": f"Activité '{nom_activite}' introuvable"},
            status=status.HTTP_404_NOT_FOUND
        )
    except Exception as e:
        return Response(
            {"message": f"Erreur lors de la suppression: {str(e)}"},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )

@api_view(['GET'])
@permission_classes([IsAuthenticated])
@role_required("Rh business unit", "Directeur Pays", "Directeur Rh Pays", "Business Unit Manager", "Assistant RH BusinessUnit", "Team manager")
def details_activite(request):
    try:
        activites = Activite.objects.all()
        serializer = ActiviteSerializer(activites, many=True)
        return Response(serializer.data, status=status.HTTP_200_OK)
    except Exception as e:
        return Response({"message": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_team_managers(request):
    try:
        team_managers = TeamManager.objects.all()
        team_managers_data = []
        
        for tm in team_managers:
            if tm.user:
                team_managers_data.append({
                    'id': tm.user.id,
                    'name': f"{tm.user.first_name} {tm.user.last_name}"
                })
        
        return Response(team_managers_data)
    except Exception as e:
        return Response(
            {'error': str(e)},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_all_equipes(request):
    try:
        equipes = Equipe.objects.all()
        equipes_data = [{
            'id': equipe.id,
            'name': equipe.nomEquipe
        } for equipe in equipes]
        return Response(equipes_data)
    except Exception as e:
        return Response(
            {'error': str(e)},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_employees_by_activity(request, activity_id):
    try:
        activite = get_object_or_404(Activite, id=activity_id)

        # Récupérer les collaborateurs liés à cette activité
        Collaborateurs = Collaborateur.objects.filter(activite=activite, user__isnull=False).select_related('user')
        # Récupérer les pilotes liés à cette activité
        pilotes = Pilote.objects.filter(activite=activite, user__isnull=False).select_related('user')

        # Fusionner les utilisateurs
        users = [c.user for c in Collaborateurs] + [p.user for p in pilotes]

        serializer = UserSerializer(users, many=True)
        return Response(serializer.data, status=status.HTTP_200_OK)
    except Activite.DoesNotExist:
        return Response({
            'error': 'Activité non trouvée',
            'activity_id': activity_id
        }, status=status.HTTP_404_NOT_FOUND)
    except Exception as e:
        return Response({
            'error': str(e)
        }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_activities_by_team(request, equipe_id):
    try:
        # Verify the team exists
        equipe = Equipe.objects.get(id=equipe_id)
        
        # Get all activities related to this team
        activities = Activite.objects.filter(equipe=equipe)
        
        serializer = ActiviteSerializer(activities, many=True)
        return Response(serializer.data, status=status.HTTP_200_OK)
    except Equipe.DoesNotExist:
        return Response({"message": "Equipe introuvable avec cet ID"}, status=status.HTTP_404_NOT_FOUND)
    except Exception as e:
        return Response({"message": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_available_activities_for_user(request, user_id=None):
    try:
        # If user_id provided, get that user, else use the authenticated user
        user = User.objects.get(id=user_id) if user_id else request.user
        
        # Determine if user is pilote or Collaborateur and get their team
        user_team = None
        if hasattr(user, 'pilote') and user.pilote and user.pilote.Equipe:
            user_team = user.pilote.Equipe
        elif hasattr(user, 'Collaborateur') and user.Collaborateur and user.Collaborateur.Equipe:
            user_team = user.Collaborateur.Equipe
        
        if not user_team:
            return Response({"message": "L'utilisateur n'appartient à aucune équipe"}, status=status.HTTP_400_BAD_REQUEST)
        
        # Get activities for this team
        activities = Activite.objects.filter(equipe=user_team)
        serializer = ActiviteSerializer(activities, many=True)
        
        return Response(serializer.data, status=status.HTTP_200_OK)
    except User.DoesNotExist:
        return Response({"message": "Utilisateur non trouvé"}, status=status.HTTP_404_NOT_FOUND)
    except Exception as e:
        return Response({"message": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['POST'])
@permission_classes([IsAuthenticated])
def assign_activity_to_user(request):
    user_id = request.data.get('user_id')
    activite_id = request.data.get('activite_id')
    
    if not user_id or not activite_id:
        return Response({"message": "L'ID de l'utilisateur et l'ID de l'activité sont requis"}, status=status.HTTP_400_BAD_REQUEST)
    
    try:
        user = User.objects.get(id=user_id)
        activite = Activite.objects.get(id=activite_id)
        
        # Check if the user is a pilote or Collaborateur
        is_pilote = hasattr(user, 'pilote') and user.pilote
        is_Collaborateur = hasattr(user, 'Collaborateur') and user.Collaborateur
        
        if not (is_pilote or is_Collaborateur):
            return Response({"message": "L'utilisateur n'est ni pilote ni Collaborateur"}, status=status.HTTP_400_BAD_REQUEST)
        
        # Get the user's team
        user_team = None
        if is_pilote:
            user_team = user.pilote.Equipe
        elif is_Collaborateur:
            user_team = user.Collaborateur.Equipe
        
        if not user_team:
            return Response({"message": "L'utilisateur n'appartient à aucune équipe"}, status=status.HTTP_400_BAD_REQUEST)
            
        # Check if the activity belongs to the user's team
        if activite.equipe != user_team:
            return Response({"message": "Cette activité n'appartient pas à l'équipe de l'utilisateur"}, status=status.HTTP_400_BAD_REQUEST)
        
        # Assign the activity to the user
        if is_pilote:
            user.pilote.activite = activite
            user.pilote.save()
        elif is_Collaborateur:
            user.Collaborateur.activite = activite
            user.Collaborateur.save()
        
        return Response({"message": "Activité assignée avec succès"}, status=status.HTTP_200_OK)
    except User.DoesNotExist:
        return Response({"message": "Utilisateur non trouvé"}, status=status.HTTP_404_NOT_FOUND)
    except Activite.DoesNotExist:
        return Response({"message": "Activité non trouvée"}, status=status.HTTP_404_NOT_FOUND)
    except Exception as e:
        return Response({"message": str(e)}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_teams_by_manager(request, manager_id):
    try:
        # Récupérer le team manager
        team_manager = get_object_or_404(TeamManager, user_id=manager_id)
        
        # Récupérer toutes les équipes associées à ce team manager
        equipes = Equipe.objects.filter(TeamManager=team_manager)
        
        equipes_data = [{
            'id': equipe.id,
            'name': equipe.nomEquipe
        } for equipe in equipes]
        
        return Response(equipes_data)
        
    except Exception as e:
        return Response(
            {'error': str(e)},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )

@api_view(['GET'])
@permission_classes([IsAuthenticated])
def get_teams_by_pilot(request, pilot_id):
    try:
        # Récupérer le pilote
        pilot = get_object_or_404(Pilote, user_id=pilot_id)
        
        # Récupérer l'équipe associée au pilote
        equipe = pilot.Equipe
        
        if equipe:
            return Response([{
                'id': equipe.id,
                'name': equipe.nomEquipe
            }])
        return Response([])
        
    except Exception as e:
        return Response(
            {'error': str(e)},
            status=status.HTTP_500_INTERNAL_SERVER_ERROR
        )