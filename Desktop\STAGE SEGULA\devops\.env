# Repository Paths (relative to devops folder)
BACKEND_REPO_PATH=../appPointage/backend
FRONTEND_REPO_PATH=../appPointageFront/Front_End

# Database Configuration
DB_ROOT_PASSWORD=rourou
DB_NAME=Access_dev
DB_USER=app_user
DB_PASSWORD=rourou
DB_HOST=db
DB_PORT=3306
DB_PORT_HOST=3308

# Django Backend Configuration
SECRET_KEY=django-insecure-)_ew*%2)cocw32ur)i+&#z0@0-bx^qk06kwauj%75kn5m9g1e&
RUNNING_IN_DOCKER=true

# Email Configuration
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=jejo ycwo kecy flp

# Frontend Configuration
NEXT_PUBLIC_API_URL=http://backend:8000

# Port Configuration
BACKEND_PORT=8000
FRONTEND_PORT=3000
