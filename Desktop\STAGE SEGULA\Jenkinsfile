pipeline {
    agent any
    
    environment {
        // Docker registry configuration
        DOCKER_REGISTRY = 'your-docker-registry.com'
        DOCKER_REPO = 'segula-pointage'
        
        // Environment files
        ENV_FILE = '.env.production'
        
        // Docker Compose
        COMPOSE_PROJECT_NAME = 'segula-pointage-prod'
    }
    
    stages {
        stage('Checkout') {
            steps {
                echo 'Checking out source code...'
                checkout scm
            }
        }
        
        stage('Environment Setup') {
            steps {
                echo 'Setting up environment...'
                script {
                    // Copy production environment file
                    sh 'cp .env.production .env'
                    
                    // Ensure Docker is available
                    sh 'docker --version'
                    sh 'docker-compose --version'
                }
            }
        }
        
        stage('Build Images') {
            steps {
                echo 'Building Docker images...'
                script {
                    // Build all services
                    sh 'docker-compose build --no-cache'
                }
            }
        }
        
        stage('Run Tests') {
            steps {
                echo 'Running tests...'
                script {
                    // Start test database
                    sh 'docker-compose up -d db'
                    
                    // Wait for database to be ready
                    sh 'sleep 30'
                    
                    // Run backend tests
                    sh '''
                        docker-compose run --rm backend sh -c "
                            python manage.py test --settings=projectd.settings.docker
                        "
                    '''
                    
                    // Run frontend tests (if you have them)
                    // sh 'docker-compose run --rm frontend npm test'
                }
            }
            post {
                always {
                    // Clean up test containers
                    sh 'docker-compose down -v'
                }
            }
        }
        
        stage('Security Scan') {
            steps {
                echo 'Running security scans...'
                script {
                    // Scan Docker images for vulnerabilities
                    sh 'docker run --rm -v /var/run/docker.sock:/var/run/docker.sock aquasec/trivy image ${COMPOSE_PROJECT_NAME}_backend:latest || true'
                    sh 'docker run --rm -v /var/run/docker.sock:/var/run/docker.sock aquasec/trivy image ${COMPOSE_PROJECT_NAME}_frontend:latest || true'
                }
            }
        }
        
        stage('Deploy to Staging') {
            when {
                branch 'develop'
            }
            steps {
                echo 'Deploying to staging environment...'
                script {
                    // Deploy to staging
                    sh '''
                        export COMPOSE_PROJECT_NAME=segula-pointage-staging
                        docker-compose -f docker-compose.yml -f docker-compose.staging.yml up -d
                    '''
                }
            }
        }
        
        stage('Deploy to Production') {
            when {
                branch 'main'
            }
            steps {
                echo 'Deploying to production environment...'
                script {
                    // Deploy to production
                    sh '''
                        export COMPOSE_PROJECT_NAME=segula-pointage-prod
                        docker-compose -f docker-compose.yml -f docker-compose.production.yml up -d
                    '''
                }
            }
        }
        
        stage('Health Check') {
            steps {
                echo 'Performing health checks...'
                script {
                    // Wait for services to start
                    sh 'sleep 60'
                    
                    // Check backend health
                    sh '''
                        for i in {1..10}; do
                            if curl -f http://localhost:8000/login/ > /dev/null 2>&1; then
                                echo "Backend is healthy"
                                break
                            fi
                            echo "Waiting for backend... ($i/10)"
                            sleep 10
                        done
                    '''
                    
                    // Check frontend health
                    sh '''
                        for i in {1..10}; do
                            if curl -f http://localhost:3000 > /dev/null 2>&1; then
                                echo "Frontend is healthy"
                                break
                            fi
                            echo "Waiting for frontend... ($i/10)"
                            sleep 10
                        done
                    '''
                }
            }
        }
    }
    
    post {
        always {
            echo 'Cleaning up...'
            // Clean up Docker resources
            sh 'docker system prune -f'
        }
        
        success {
            echo 'Pipeline completed successfully!'
            // Send success notification
            emailext (
                subject: "✅ Jenkins Build Success: ${env.JOB_NAME} - ${env.BUILD_NUMBER}",
                body: "The build completed successfully.\n\nBuild URL: ${env.BUILD_URL}",
                to: "<EMAIL>"
            )
        }
        
        failure {
            echo 'Pipeline failed!'
            // Send failure notification
            emailext (
                subject: "❌ Jenkins Build Failed: ${env.JOB_NAME} - ${env.BUILD_NUMBER}",
                body: "The build failed. Please check the logs.\n\nBuild URL: ${env.BUILD_URL}",
                to: "<EMAIL>"
            )
        }
    }
}
