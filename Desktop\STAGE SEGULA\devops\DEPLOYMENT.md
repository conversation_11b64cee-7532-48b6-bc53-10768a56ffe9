# Segula Pointage - Deployment Guide

This guide covers deployment strategies for the multi-repository Segula Pointage application.

## 🏗️ Repository Structure

```
Project Organization:
├── segula-backend/       # Backend Repository (Django)
├── segula-frontend/      # Frontend Repository (Next.js)
└── segula-devops/        # DevOps Repository (this repo)
```

## 🚀 Deployment Scenarios

### 1. Local Development

**Quick Setup:**
```bash
# Clone DevOps repo and run setup
git clone https://github.com/your-org/segula-devops.git devops
cd devops
./setup.sh  # or setup.ps1 on Windows

# Start development environment
docker-compose up -d
```

**Manual Setup:**
```bash
# Clone all repositories
git clone https://github.com/your-org/segula-backend.git backend
git clone https://github.com/your-org/segula-frontend.git frontend
git clone https://github.com/your-org/segula-devops.git devops

# Configure and start
cd devops
cp .env.example .env
# Edit .env with your settings
docker-compose up -d
```

### 2. Staging Environment

```bash
# Use staging configuration
cd devops
cp .env.example .env.staging
# Update staging settings in .env.staging
cp .env.staging .env

# Deploy to staging
docker-compose -f docker-compose.yml -f docker-compose.staging.yml up -d
```

### 3. Production Deployment

```bash
# Production setup
cd devops
cp .env.production .env
# Update production settings in .env

# Deploy with production configuration
docker-compose -f docker-compose.yml -f docker-compose.production.yml up -d
```

## 🔄 CI/CD Deployment

### Jenkins Pipeline Deployment

The Jenkins pipeline automatically:

1. **Clones all repositories** in parallel
2. **Builds Docker images** for backend and frontend
3. **Runs tests** on both applications
4. **Performs security scans**
5. **Deploys to staging/production** based on branch
6. **Runs health checks** to verify deployment

**Setup Jenkins:**
```bash
cd devops
./jenkins-setup.sh
```

**Pipeline Triggers:**
- **Main branch** → Production deployment
- **Develop branch** → Staging deployment
- **Feature branches** → Build and test only

### Manual CI/CD Steps

If you want to replicate the CI/CD process manually:

```bash
# 1. Clone repositories
git clone https://github.com/your-org/segula-backend.git backend
git clone https://github.com/your-org/segula-frontend.git frontend
git clone https://github.com/your-org/segula-devops.git devops

# 2. Setup environment
cd devops
cp .env.production .env

# 3. Build images
docker-compose build --no-cache

# 4. Run tests
docker-compose run --rm backend python manage.py test
# docker-compose run --rm frontend npm test  # if you have frontend tests

# 5. Security scan (optional)
docker run --rm -v /var/run/docker.sock:/var/run/docker.sock aquasec/trivy image devops_backend:latest
docker run --rm -v /var/run/docker.sock:/var/run/docker.sock aquasec/trivy image devops_frontend:latest

# 6. Deploy
docker-compose -f docker-compose.yml -f docker-compose.production.yml up -d

# 7. Health check
curl -f http://localhost:8000/login/  # Should return 405 Method Not Allowed
curl -f http://localhost:3000         # Should return 200 OK
```

## 🌐 Production Deployment with Nginx

For production with SSL and reverse proxy:

```bash
# 1. Setup SSL certificates
mkdir -p nginx/ssl
# Copy your SSL certificates to nginx/ssl/

# 2. Update nginx configuration
# Edit nginx/nginx.conf with your domain

# 3. Deploy with Nginx
docker-compose -f docker-compose.yml -f docker-compose.production.yml up -d

# 4. Verify SSL
curl -k https://your-domain.com/health
```

## 🔧 Environment Configuration

### Development (.env)
```bash
BACKEND_REPO_PATH=../backend
FRONTEND_REPO_PATH=../frontend
DB_PASSWORD=dev_password
NEXT_PUBLIC_API_URL=http://backend:8000
```

### Staging (.env.staging)
```bash
BACKEND_REPO_PATH=./backend
FRONTEND_REPO_PATH=./frontend
DB_PASSWORD=staging_password
NEXT_PUBLIC_API_URL=http://staging-api.company.com
```

### Production (.env.production)
```bash
BACKEND_REPO_PATH=./backend
FRONTEND_REPO_PATH=./frontend
DB_PASSWORD=secure_production_password
NEXT_PUBLIC_API_URL=https://api.company.com
DJANGO_DEBUG=false
DJANGO_ALLOWED_HOSTS=api.company.com,company.com
```

## 📊 Monitoring and Health Checks

### Application Health
```bash
# Backend health
curl http://localhost:8000/login/
# Expected: 405 Method Not Allowed (endpoint exists but GET not allowed)

# Frontend health
curl http://localhost:3000
# Expected: 200 OK with HTML content

# Database health
docker-compose exec db mysqladmin ping -u root -p
```

### Container Status
```bash
# Check all containers
docker-compose ps

# View logs
docker-compose logs -f

# Resource usage
docker stats
```

### Production Monitoring
```bash
# Nginx health (production only)
curl http://localhost/health
# Expected: 200 "healthy"

# SSL certificate check
openssl s_client -connect your-domain.com:443 -servername your-domain.com
```

## 🔄 Update Procedures

### Code Updates

**Backend Updates:**
```bash
cd backend
git pull origin main
cd ../devops
docker-compose build backend
docker-compose up -d backend
```

**Frontend Updates:**
```bash
cd frontend
git pull origin main
cd ../devops
docker-compose build frontend
docker-compose up -d frontend
```

**DevOps Updates:**
```bash
cd devops
git pull origin main
docker-compose down
docker-compose up -d
```

### Database Updates

**Backup before updates:**
```bash
docker-compose exec db mysqldump -u root -p${DB_ROOT_PASSWORD} ${DB_NAME} > backup_$(date +%Y%m%d_%H%M%S).sql
```

**Apply migrations:**
```bash
docker-compose exec backend python manage.py migrate
```

## 🆘 Troubleshooting

### Common Issues

**Repository not found:**
```bash
# Check repository paths
ls -la ../backend ../frontend

# Update .env with correct paths
BACKEND_REPO_PATH=../backend
FRONTEND_REPO_PATH=../frontend
```

**Build failures:**
```bash
# Clean build
docker-compose down
docker system prune -f
docker-compose build --no-cache
docker-compose up -d
```

**Network issues:**
```bash
# Recreate network
docker-compose down
docker network prune
docker-compose up -d
```

**Database connection issues:**
```bash
# Reset database
docker-compose down -v
docker-compose up -d db
# Wait for database to be healthy
docker-compose up -d
```

### Rollback Procedures

**Quick rollback:**
```bash
# Stop current deployment
docker-compose down

# Checkout previous version
git checkout HEAD~1

# Redeploy
docker-compose up -d
```

**Database rollback:**
```bash
# Restore from backup
docker-compose exec -T db mysql -u root -p${DB_ROOT_PASSWORD} ${DB_NAME} < backup_file.sql
```

## 📞 Support

For deployment issues:
1. Check container logs: `docker-compose logs`
2. Verify environment configuration
3. Ensure all repositories are accessible
4. Check network connectivity between services
5. Verify SSL certificates (production)

## 🔐 Security Considerations

### Production Security
- Use strong passwords for all services
- Enable SSL/TLS for all external connections
- Regularly update base images
- Scan for vulnerabilities
- Limit network exposure
- Use secrets management for sensitive data

### Environment Isolation
- Separate databases for each environment
- Different API keys and secrets
- Isolated networks
- Resource limits in production
