0000000000000000000000000000000000000000 532a633297d84ab1545205ccd61477592262b771 RouaAyadi <<EMAIL>> 1751451280 +0100	commit (initial): first commit
532a633297d84ab1545205ccd61477592262b771 0000000000000000000000000000000000000000 RouaAyadi <<EMAIL>> 1751451280 +0100	Branch: renamed refs/heads/master to refs/heads/main
0000000000000000000000000000000000000000 532a633297d84ab1545205ccd61477592262b771 RouaAyadi <<EMAIL>> 1751451280 +0100	Branch: renamed refs/heads/master to refs/heads/main
532a633297d84ab1545205ccd61477592262b771 e6b5b5e89fc8f3f2c6304e2462a8ad2160ecc106 RouaAyadi <<EMAIL>> 1751533564 +0100	commit: change jenkins file
e6b5b5e89fc8f3f2c6304e2462a8ad2160ecc106 1cc7f8e0718532bd363251215734590b6ed24c30 RouaAyadi <<EMAIL>> 1751533590 +0100	pull: Merge made by the 'ort' strategy.
1cc7f8e0718532bd363251215734590b6ed24c30 aa0bc11a9b42a9a210148ae9fc2e230cc1977cd7 RouaAyadi <<EMAIL>> 1751615052 +0100	commit: update docker compose
aa0bc11a9b42a9a210148ae9fc2e230cc1977cd7 690c1c7768c2f7b68f475c8398f14fa0695bcce2 RouaAyadi <<EMAIL>> 1751619810 +0100	commit: try remove
690c1c7768c2f7b68f475c8398f14fa0695bcce2 690c1c7768c2f7b68f475c8398f14fa0695bcce2 RouaAyadi <<EMAIL>> 1752193295 +0100	checkout: moving from main to add-test
