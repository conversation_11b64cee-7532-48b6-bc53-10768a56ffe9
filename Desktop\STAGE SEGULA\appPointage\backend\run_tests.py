#!/usr/bin/env python
"""
Test runner script for Django backend
This script runs all tests and provides detailed output
"""

import os
import sys
import django
from django.conf import settings
from django.test.utils import get_runner
from django.core.management import execute_from_command_line


def run_tests():
    """Run all Django tests"""
    
    print("🧪 Starting Django Backend Tests...")
    print("=" * 50)
    
    # Set up Django environment
    os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'projectd.settings.docker')
    django.setup()
    
    # Get the Django test runner
    TestRunner = get_runner(settings)
    test_runner = TestRunner()
    
    # List of test modules to run
    test_modules = [
        'gestion_utilisateurs.test_models',
        'gestion_utilisateurs.test_views',
    ]
    
    print(f"📋 Running tests for modules: {', '.join(test_modules)}")
    print("-" * 50)
    
    # Run tests
    failures = test_runner.run_tests(test_modules)
    
    print("-" * 50)
    
    if failures:
        print(f"❌ Tests completed with {failures} failure(s)")
        return False
    else:
        print("✅ All tests passed successfully!")
        return True


def run_specific_tests():
    """Run specific test categories"""
    
    print("🎯 Running Specific Test Categories...")
    print("=" * 50)
    
    test_categories = {
        'models': 'gestion_utilisateurs.test_models',
        'views': 'gestion_utilisateurs.test_views',
    }
    
    results = {}
    
    for category, module in test_categories.items():
        print(f"\n🔍 Running {category.upper()} tests...")
        print("-" * 30)
        
        # Run tests for this category
        TestRunner = get_runner(settings)
        test_runner = TestRunner(verbosity=2)
        failures = test_runner.run_tests([module])
        
        results[category] = failures == 0
        
        if failures:
            print(f"❌ {category.upper()} tests failed")
        else:
            print(f"✅ {category.upper()} tests passed")
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 TEST SUMMARY")
    print("=" * 50)
    
    for category, passed in results.items():
        status = "✅ PASSED" if passed else "❌ FAILED"
        print(f"{category.upper()}: {status}")
    
    all_passed = all(results.values())
    print(f"\nOverall Result: {'✅ ALL TESTS PASSED' if all_passed else '❌ SOME TESTS FAILED'}")
    
    return all_passed


def run_coverage_tests():
    """Run tests with coverage report"""
    
    print("📊 Running Tests with Coverage...")
    print("=" * 50)
    
    try:
        import coverage
        
        # Start coverage
        cov = coverage.Coverage()
        cov.start()
        
        # Run tests
        success = run_tests()
        
        # Stop coverage and generate report
        cov.stop()
        cov.save()
        
        print("\n📈 Coverage Report:")
        print("-" * 30)
        cov.report()
        
        return success
        
    except ImportError:
        print("⚠️  Coverage package not installed. Running tests without coverage...")
        return run_tests()


def main():
    """Main function to run tests based on command line arguments"""
    
    if len(sys.argv) > 1:
        command = sys.argv[1]
        
        if command == 'coverage':
            success = run_coverage_tests()
        elif command == 'specific':
            success = run_specific_tests()
        elif command == 'models':
            # Run only model tests
            os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'projectd.settings.docker')
            django.setup()
            TestRunner = get_runner(settings)
            test_runner = TestRunner(verbosity=2)
            failures = test_runner.run_tests(['gestion_utilisateurs.test_models'])
            success = failures == 0
        elif command == 'views':
            # Run only view tests
            os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'projectd.settings.docker')
            django.setup()
            TestRunner = get_runner(settings)
            test_runner = TestRunner(verbosity=2)
            failures = test_runner.run_tests(['gestion_utilisateurs.test_views'])
            success = failures == 0
        else:
            print(f"❌ Unknown command: {command}")
            print("Available commands: coverage, specific, models, views")
            success = False
    else:
        success = run_tests()
    
    # Exit with appropriate code
    sys.exit(0 if success else 1)


if __name__ == '__main__':
    main()
