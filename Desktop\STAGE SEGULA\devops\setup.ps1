# Segula Pointage DevOps Setup Script (PowerShell)
# This script sets up the development environment with separate repositories

param(
    [string]$BackendRepo = "https://github.com/your-org/segula-backend.git",
    [string]$FrontendRepo = "https://github.com/your-org/segula-frontend.git"
)

Write-Host "🚀 Setting up Segula Pointage DevOps Environment..." -ForegroundColor Green

# Check if Git is installed
try {
    git --version | Out-Null
    Write-Host "✅ Git is available" -ForegroundColor Green
} catch {
    Write-Host "❌ Git is not installed. Please install Git first." -ForegroundColor Red
    exit 1
}

# Check if Docker is installed
try {
    docker --version | Out-Null
    Write-Host "✅ Docker is available" -ForegroundColor Green
} catch {
    Write-Host "❌ Docker is not installed. Please install Docker first." -ForegroundColor Red
    exit 1
}

# Check if Docker Compose is installed
try {
    docker-compose --version | Out-Null
    Write-Host "✅ Docker Compose is available" -ForegroundColor Green
} catch {
    Write-Host "❌ Docker Compose is not installed. Please install Docker Compose first." -ForegroundColor Red
    exit 1
}

# Function to clone repository if it doesn't exist
function Clone-Repository {
    param(
        [string]$RepoUrl,
        [string]$TargetDir,
        [string]$RepoName
    )
    
    if (-not (Test-Path $TargetDir)) {
        Write-Host "📥 Cloning $RepoName repository..." -ForegroundColor Yellow
        git clone $RepoUrl $TargetDir
    } else {
        Write-Host "✅ $RepoName repository already exists, pulling latest changes..." -ForegroundColor Green
        Push-Location $TargetDir
        git pull origin main
        Pop-Location
    }
}

# Clone repositories
Write-Host "📂 Setting up repository structure..." -ForegroundColor Yellow

# Go to parent directory to clone repos alongside devops
Set-Location ..

# Clone backend repository
Clone-Repository -RepoUrl $BackendRepo -TargetDir "backend" -RepoName "Backend"

# Clone frontend repository  
Clone-Repository -RepoUrl $FrontendRepo -TargetDir "frontend" -RepoName "Frontend"

# Return to devops directory
Set-Location devops

# Copy environment file
Write-Host "⚙️ Setting up environment configuration..." -ForegroundColor Yellow
if (-not (Test-Path ".env")) {
    if (Test-Path ".env.example") {
        Copy-Item ".env.example" ".env"
    } else {
        Copy-Item ".env" ".env.backup" -ErrorAction SilentlyContinue
    }
    Write-Host "✅ Environment file created. Please update .env with your settings." -ForegroundColor Green
} else {
    Write-Host "✅ Environment file already exists." -ForegroundColor Green
}

# Create necessary directories
Write-Host "📁 Creating necessary directories..." -ForegroundColor Yellow
New-Item -ItemType Directory -Force -Path "logs" | Out-Null
New-Item -ItemType Directory -Force -Path "nginx/ssl" | Out-Null

# Display repository structure
Write-Host ""
Write-Host "📋 Repository Structure:" -ForegroundColor Cyan
Write-Host "├── backend/          # Backend repository (Django)" -ForegroundColor White
Write-Host "├── frontend/         # Frontend repository (Next.js)" -ForegroundColor White
Write-Host "└── devops/           # DevOps repository (this repo)" -ForegroundColor White
Write-Host "    ├── docker-compose.yml" -ForegroundColor Gray
Write-Host "    ├── .env" -ForegroundColor Gray
Write-Host "    ├── Jenkinsfile" -ForegroundColor Gray
Write-Host "    └── nginx/" -ForegroundColor Gray
Write-Host ""

# Check if repositories exist
Write-Host "🔍 Checking repository status:" -ForegroundColor Yellow
if (Test-Path "../backend") {
    Write-Host "✅ Backend repository: Ready" -ForegroundColor Green
} else {
    Write-Host "❌ Backend repository: Missing" -ForegroundColor Red
    Write-Host "   Please update BackendRepo parameter with the correct URL" -ForegroundColor Yellow
}

if (Test-Path "../frontend") {
    Write-Host "✅ Frontend repository: Ready" -ForegroundColor Green
} else {
    Write-Host "❌ Frontend repository: Missing" -ForegroundColor Red
    Write-Host "   Please update FrontendRepo parameter with the correct URL" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "🎯 Next Steps:" -ForegroundColor Cyan
Write-Host "1. Update repository URLs in this script (setup.ps1)" -ForegroundColor White
Write-Host "2. Update .env file with your configuration" -ForegroundColor White
Write-Host "3. Run: docker-compose up -d" -ForegroundColor White
Write-Host "4. Access application at:" -ForegroundColor White
Write-Host "   - Frontend: http://localhost:3000" -ForegroundColor Gray
Write-Host "   - Backend: http://localhost:8000" -ForegroundColor Gray
Write-Host ""
Write-Host "📚 For Jenkins setup:" -ForegroundColor Cyan
Write-Host "   ./jenkins-setup.sh" -ForegroundColor White
Write-Host ""
Write-Host "✅ DevOps environment setup completed!" -ForegroundColor Green
