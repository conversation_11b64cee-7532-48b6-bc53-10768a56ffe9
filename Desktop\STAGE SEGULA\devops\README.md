# Segula Pointage - DevOps Repository

This repository contains all DevOps configurations and deployment scripts for the Segula Pointage application, designed to work with separate backend and frontend repositories.

## 🏗️ Multi-Repository Architecture

```
Project Structure:
├── segula-backend/       # Django Backend Repository
├── segula-frontend/      # Next.js Frontend Repository  
└── segula-devops/        # DevOps Repository (this repo)
    ├── docker-compose.yml
    ├── .env
    ├── Jenkinsfile
    ├── nginx/
    └── setup scripts
```
