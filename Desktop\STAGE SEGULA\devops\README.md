# Segula Pointage - DevOps Repository

This repository contains all DevOps configurations and deployment scripts for the Segula Pointage application, designed to work with separate backend and frontend repositories.

## 🏗️ Multi-Repository Architecture

```
Project Structure:
├── segula-backend/       # Django Backend Repository
├── segula-frontend/      # Next.js Frontend Repository  
└── segula-devops/        # DevOps Repository (this repo)
    ├── docker-compose.yml
    ├── .env
    ├── Jenkinsfile
    ├── nginx/
    └── setup scripts
```

## 🚀 Quick Start

### Prerequisites
- Git
- Docker & Docker Compose
- (Optional) Jenkins for CI/CD

### Setup Development Environment

**Linux/macOS:**
```bash
# Clone this DevOps repository
git clone https://github.com/your-org/segula-devops.git
cd segula-devops

# Run setup script (will clone backend and frontend repos)
chmod +x setup.sh
./setup.sh
```

**Windows (PowerShell):**
```powershell
# Clone this DevOps repository
git clone https://github.com/your-org/segula-devops.git
cd segula-devops

# Run setup script (will clone backend and frontend repos)
.\setup.ps1
```

### Manual Setup
If you prefer to set up manually:

```bash
# Clone all repositories in the same parent directory
git clone https://github.com/your-org/segula-backend.git ../backend
git clone https://github.com/your-org/segula-frontend.git ../frontend
git clone https://github.com/your-org/segula-devops.git devops

cd devops
cp .env.example .env  # Edit with your settings
```

### Start the Application

```bash
# From the devops directory
docker-compose up -d
```

**Access Points:**
- Frontend: http://localhost:3000
- Backend API: http://localhost:8000
- Database: localhost:3308

## 📁 Repository Contents

### Core Files
- **`docker-compose.yml`** - Main orchestration file
- **`.env`** - Development environment variables
- **`.env.production`** - Production environment template
- **`Jenkinsfile`** - CI/CD pipeline definition

### Environment-Specific Files
- **`docker-compose.production.yml`** - Production overrides
- **`docker-compose.staging.yml`** - Staging environment
- **`nginx/nginx.conf`** - Production reverse proxy

### Setup Scripts
- **`setup.sh`** - Linux/macOS setup script
- **`setup.ps1`** - Windows PowerShell setup script
- **`jenkins-setup.sh`** - Jenkins installation script

## ⚙️ Configuration

### Environment Variables

The `.env` file contains all configuration:

```bash
# Repository Paths (relative to devops folder)
BACKEND_REPO_PATH=../backend
FRONTEND_REPO_PATH=../frontend

# Database Configuration
DB_ROOT_PASSWORD=rourou
DB_NAME=Access_dev
DB_USER=app_user
DB_PASSWORD=rourou

# Application Settings
SECRET_KEY=your-django-secret-key
NEXT_PUBLIC_API_URL=http://backend:8000

# Port Configuration
BACKEND_PORT=8000
FRONTEND_PORT=3000
DB_PORT_HOST=3308
```

### Repository URLs

For CI/CD, update these in `.env.production`:

```bash
BACKEND_REPO_URL=https://github.com/your-org/segula-backend.git
FRONTEND_REPO_URL=https://github.com/your-org/segula-frontend.git
```

## 🐳 Docker Services

### Database (MariaDB)
- **Image:** mariadb:10.11
- **Port:** 3308:3306
- **Health Check:** Automatic MySQL ping

### Backend (Django)
- **Build Context:** `../backend`
- **Port:** 8000:8000
- **Dependencies:** Database health check

### Frontend (Next.js)
- **Build Context:** `../frontend`
- **Port:** 3000:3000
- **Dependencies:** Backend service

## 🔄 CI/CD Pipeline

### Jenkins Setup

```bash
# Install and configure Jenkins
./jenkins-setup.sh

# Access Jenkins
# URL: http://localhost:8080
# Username: admin
# Password: admin123
```

### Pipeline Stages

1. **Checkout Repositories** - Clone all three repos in parallel
2. **Environment Setup** - Configure production environment
3. **Build Images** - Build Docker containers
4. **Run Tests** - Execute test suites
5. **Security Scan** - Vulnerability scanning
6. **Deploy** - Deploy to staging/production
7. **Health Check** - Verify deployment

### Branch Strategy

- **`main`** → Production deployment
- **`develop`** → Staging deployment
- **Feature branches** → Build and test only

## 🌍 Environment Management

### Development
```bash
# Use default .env file
docker-compose up -d
```

### Staging
```bash
# Use staging configuration
docker-compose -f docker-compose.yml -f docker-compose.staging.yml up -d
```

### Production
```bash
# Use production configuration
cp .env.production .env
docker-compose -f docker-compose.yml -f docker-compose.production.yml up -d
```

## 🔒 Security Features

- Environment-based configuration
- SSL/TLS termination (production)
- Rate limiting on API endpoints
- Security headers
- Vulnerability scanning
- Secrets management

## 🛠️ Development Commands

### Basic Operations
```bash
# Start all services
docker-compose up -d

# View logs
docker-compose logs -f [service_name]

# Restart a service
docker-compose restart [service_name]

# Stop all services
docker-compose down

# Clean up (remove volumes)
docker-compose down -v
```

### Repository Updates
```bash
# Update backend code
cd ../backend
git pull origin main
cd ../devops
docker-compose build backend
docker-compose up -d backend

# Update frontend code
cd ../frontend
git pull origin main
cd ../devops
docker-compose build frontend
docker-compose up -d frontend
```

## 📊 Monitoring

### Health Checks
- **Database:** MySQL ping command
- **Backend:** Django health endpoint
- **Frontend:** Next.js server response

### Logs
```bash
# View all logs
docker-compose logs

# View specific service logs
docker-compose logs backend
docker-compose logs frontend
docker-compose logs db

# Follow logs in real-time
docker-compose logs -f
```

## 🔧 Customization

### Adding Environment Variables

1. **Add to `.env` file:**
   ```bash
   NEW_VARIABLE=value
   ```

2. **Update `docker-compose.yml`:**
   ```yaml
   environment:
     - NEW_VARIABLE=${NEW_VARIABLE}
   ```

3. **Restart services:**
   ```bash
   docker-compose up -d
   ```

### Repository Structure Changes

If you need to change the repository structure:

1. Update paths in `.env`:
   ```bash
   BACKEND_REPO_PATH=./path/to/backend
   FRONTEND_REPO_PATH=./path/to/frontend
   ```

2. Update setup scripts (`setup.sh`, `setup.ps1`)

3. Update Jenkinsfile repository URLs

## 🆘 Troubleshooting

### Common Issues

**Repositories not found:**
```bash
# Check repository paths
ls -la ../backend ../frontend

# Update .env with correct paths
BACKEND_REPO_PATH=../backend
FRONTEND_REPO_PATH=../frontend
```

**Port conflicts:**
```bash
# Check what's using the ports
netstat -tulpn | grep :3000
netstat -tulpn | grep :8000

# Update ports in .env if needed
BACKEND_PORT=8001
FRONTEND_PORT=3001
```

**Database connection issues:**
```bash
# Check database health
docker-compose exec db mysqladmin ping -u root -p

# Reset database
docker-compose down -v
docker-compose up -d
```

## 📞 Support

For issues or questions:
1. Check the logs: `docker-compose logs`
2. Verify repository structure and paths
3. Ensure all required ports are available
4. Check Docker and Docker Compose versions

## 🔄 Updates

### Updating DevOps Configuration
```bash
git pull origin main
docker-compose down
docker-compose up -d
```

### Updating Application Code
```bash
# Backend updates
cd ../backend && git pull origin main && cd ../devops
docker-compose build backend && docker-compose up -d backend

# Frontend updates  
cd ../frontend && git pull origin main && cd ../devops
docker-compose build frontend && docker-compose up -d frontend
```
#   a p p P o i n t a g e D e v o p s 
 
 