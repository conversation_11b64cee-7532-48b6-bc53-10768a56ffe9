# Repository Paths (relative to devops folder)
BACKEND_REPO_PATH=../backend
FRONTEND_REPO_PATH=../frontend

# Repository URLs (for CI/CD)
BACKEND_REPO_URL=https://github.com/your-org/segula-backend.git
FRONTEND_REPO_URL=https://github.com/your-org/segula-frontend.git

# Database Configuration
DB_ROOT_PASSWORD=your_secure_password
DB_NAME=Access_dev
DB_USER=app_user
DB_PASSWORD=your_secure_password
DB_HOST=db
DB_PORT=3306
DB_PORT_HOST=3308

# Django Backend Configuration
SECRET_KEY=your-django-secret-key-change-this
RUNNING_IN_DOCKER=true

# Email Configuration
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your-email-password

# Frontend Configuration
NEXT_PUBLIC_API_URL=http://backend:8000

# Port Configuration
BACKEND_PORT=8000
FRONTEND_PORT=3000

# Production Settings (for production environment)
DJANGO_DEBUG=false
DJANGO_ALLOWED_HOSTS=localhost,127.0.0.1,your-domain.com
