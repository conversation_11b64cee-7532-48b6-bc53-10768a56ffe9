version: '3.8'

services:
  db:
    image: mariadb:10.11
    environment:
      MYSQL_ROOT_PASSWORD: rootpassword
      MYSQL_DATABASE: pointage_db
      MYSQL_USER: pointage_user
      MYSQL_PASSWORD: pointage_password
    ports:
      - "3306:3306"
    volumes:
      - db_data:/var/lib/mysql

  backend:
    build: ./appPointage/backend
    environment:
      - DB_NAME=pointage_db
      - DB_USER=pointage_user
      - DB_PASSWORD=pointage_password
      - DB_HOST=db
      - DB_PORT=3306
      - SECRET_KEY=your-secret-key-here
    ports:
      - "8000:8000"
    depends_on:
      - db

  frontend:
    build: ./appPointageFront/Front_End
    environment:
      - NEXT_PUBLIC_API_URL=http://localhost:8000
    ports:
      - "3000:3000"
    depends_on:
      - backend

volumes:
  db_data:
