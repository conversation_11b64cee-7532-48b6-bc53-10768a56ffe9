
services:
  db:
    image: mariadb:10.11
    environment:
      MYSQL_ROOT_PASSWORD: rourou
      MYSQL_DATABASE: Access_dev
      MYSQL_USER: app_user
      MYSQL_PASSWORD: rourou
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-u", "root", "-prourou", "-h", "localhost"]
      interval: 10s
      timeout: 5s
      retries: 5
    ports:
      - "3308:3306"
    volumes:
      - db_data:/var/lib/mysql

  backend:
    build: ./appPointage/backend
    environment:
      - RUNNING_IN_DOCKER=true
      - DB_NAME=Access_dev
      - DB_USER=app_user
      - DB_PASSWORD=rourou
      - DB_HOST=db
      - DB_PORT=3306
      - SECRET_KEY=django-insecure-)_ew*%2)cocw32ur)i+&#z0@0-bx^qk06kwauj%75kn5m9g1e&
      - EMAIL_HOST_USER=<EMAIL>
      - EMAIL_HOST_PASSWORD=jejo ycwo kecy flp
    ports:
      - "8000:8000"
    depends_on:
      db:
        condition: service_healthy


  frontend:
    build: ./appPointageFront/Front_End
    environment:
      - NEXT_PUBLIC_API_URL=http://backend:8000
    ports:
      - "3000:3000"
    depends_on:
      - backend

volumes:
  db_data:
