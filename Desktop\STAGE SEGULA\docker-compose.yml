
services:
  db:
    image: mariadb:10.11
    environment:
      MYSQL_ROOT_PASSWORD: ${DB_ROOT_PASSWORD}
      MYSQL_DATABASE: ${DB_NAME}
      MYSQL_USER: ${DB_USER}
      MYSQL_PASSWORD: ${DB_PASSWORD}
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-u", "root", "-p${DB_ROOT_PASSWORD}", "-h", "localhost"]
      interval: 10s
      timeout: 5s
      retries: 5
    ports:
      - "${DB_PORT_HOST}:3306"
    volumes:
      - db_data:/var/lib/mysql

  backend:
    build: ./appPointage/backend
    environment:
      - RUNNING_IN_DOCKER=${RUNNING_IN_DOCKER}
      - DB_NAME=${DB_NAME}
      - DB_USER=${DB_USER}
      - DB_PASSWORD=${DB_PASSWORD}
      - DB_HOST=${DB_HOST}
      - DB_PORT=${DB_PORT}
      - SECRET_KEY=${SECRET_KEY}
      - EMAIL_HOST_USER=${EMAIL_HOST_USER}
      - EMAIL_HOST_PASSWORD=${EMAIL_HOST_PASSWORD}
    ports:
      - "${BACKEND_PORT}:8000"
    depends_on:
      db:
        condition: service_healthy


  frontend:
    build: ./appPointageFront/Front_End
    environment:
      - NEXT_PUBLIC_API_URL=${NEXT_PUBLIC_API_URL}
    ports:
      - "${FRONTEND_PORT}:3000"
    depends_on:
      - backend

volumes:
  db_data:

