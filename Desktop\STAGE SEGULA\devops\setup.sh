#!/bin/bash

# Segula Pointage DevOps Setup Script
# This script sets up the development environment with separate repositories

set -e

echo "🚀 Setting up Segula Pointage DevOps Environment..."

# Configuration
BACKEND_REPO="https://github.com/your-org/segula-backend.git"
FRONTEND_REPO="https://github.com/your-org/segula-frontend.git"

# Check if Git is installed
if ! command -v git &> /dev/null; then
    echo "❌ Git is not installed. Please install Git first."
    exit 1
fi

# Check if Docker is installed
if ! command -v docker &> /dev/null; then
    echo "❌ Docker is not installed. Please install Docker first."
    exit 1
fi

# Check if Docker Compose is installed
if ! command -v docker-compose &> /dev/null; then
    echo "❌ Docker Compose is not installed. Please install Docker Compose first."
    exit 1
fi

# Function to clone repository if it doesn't exist
clone_repo() {
    local repo_url=$1
    local target_dir=$2
    local repo_name=$3
    
    if [ ! -d "$target_dir" ]; then
        echo "📥 Cloning $repo_name repository..."
        git clone "$repo_url" "$target_dir"
    else
        echo "✅ $repo_name repository already exists, pulling latest changes..."
        cd "$target_dir"
        git pull origin main
        cd ..
    fi
}

# Clone repositories
echo "📂 Setting up repository structure..."

# Go to parent directory to clone repos alongside devops
cd ..

# Clone backend repository
clone_repo "$BACKEND_REPO" "backend" "Backend"

# Clone frontend repository  
clone_repo "$FRONTEND_REPO" "frontend" "Frontend"

# Return to devops directory
cd devops

# Copy environment file
echo "⚙️ Setting up environment configuration..."
if [ ! -f ".env" ]; then
    cp ".env.example" ".env" 2>/dev/null || cp ".env" ".env.backup"
    echo "✅ Environment file created. Please update .env with your settings."
else
    echo "✅ Environment file already exists."
fi

# Create necessary directories
echo "📁 Creating necessary directories..."
mkdir -p logs
mkdir -p nginx/ssl

# Set permissions
echo "🔐 Setting permissions..."
chmod +x setup.sh
chmod +x jenkins-setup.sh

# Display repository structure
echo ""
echo "📋 Repository Structure:"
echo "├── backend/          # Backend repository (Django)"
echo "├── frontend/         # Frontend repository (Next.js)"
echo "└── devops/           # DevOps repository (this repo)"
echo "    ├── docker-compose.yml"
echo "    ├── .env"
echo "    ├── Jenkinsfile"
echo "    └── nginx/"
echo ""

# Check if repositories exist
echo "🔍 Checking repository status:"
if [ -d "../backend" ]; then
    echo "✅ Backend repository: Ready"
else
    echo "❌ Backend repository: Missing"
    echo "   Please update BACKEND_REPO in this script with the correct URL"
fi

if [ -d "../frontend" ]; then
    echo "✅ Frontend repository: Ready"
else
    echo "❌ Frontend repository: Missing"
    echo "   Please update FRONTEND_REPO in this script with the correct URL"
fi

echo ""
echo "🎯 Next Steps:"
echo "1. Update repository URLs in this script (setup.sh)"
echo "2. Update .env file with your configuration"
echo "3. Run: docker-compose up -d"
echo "4. Access application at:"
echo "   - Frontend: http://localhost:3000"
echo "   - Backend: http://localhost:8000"
echo ""
echo "📚 For Jenkins setup:"
echo "   ./jenkins-setup.sh"
echo ""
echo "✅ DevOps environment setup completed!"
