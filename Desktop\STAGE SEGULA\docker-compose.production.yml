# Production overrides for docker-compose.yml
version: '3.8'

services:
  db:
    restart: always
    volumes:
      - prod_db_data:/var/lib/mysql
    environment:
      MYSQL_ROOT_PASSWORD: ${DB_ROOT_PASSWORD}
      MYSQL_DATABASE: ${DB_NAME}
      MYSQL_USER: ${DB_USER}
      MYSQL_PASSWORD: ${DB_PASSWORD}

  backend:
    restart: always
    environment:
      - DJANGO_DEBUG=false
      - DJANGO_ALLOWED_HOSTS=${DJANGO_ALLOWED_HOSTS}
    volumes:
      - static_files:/app/static
      - media_files:/app/media
    deploy:
      resources:
        limits:
          memory: 1G
          cpus: '0.5'

  frontend:
    restart: always
    deploy:
      resources:
        limits:
          memory: 512M
          cpus: '0.3'

  # Nginx reverse proxy for production
  nginx:
    image: nginx:alpine
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/ssl:/etc/nginx/ssl:ro
      - static_files:/var/www/static:ro
      - media_files:/var/www/media:ro
    depends_on:
      - backend
      - frontend
    restart: always
    networks:
      - app_network

volumes:
  prod_db_data:
  static_files:
  media_files:
