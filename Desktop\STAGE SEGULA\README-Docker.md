# Simple Docker Setup

## Quick Start

1. **Navigate to project directory:**
   ```bash
   cd "Desktop/STAGE SEGULA"
   ```

2. **Start everything:**
   ```bash
   docker-compose up --build
   ```

3. **Access the application:**
   - Frontend: http://localhost:3000
   - Backend: http://localhost:8000
   - Database: localhost:3306

## That's it!

The setup includes:
- MariaDB database on port 3306
- Django backend on port 8000
- Next.js frontend on port 3000

## Useful commands:

```bash
# Stop everything
docker-compose down

# View logs
docker-compose logs

# Restart
docker-compose restart
```
