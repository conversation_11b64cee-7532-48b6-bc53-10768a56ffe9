# Staging overrides for docker-compose.yml
version: '3.8'

services:
  db:
    restart: unless-stopped
    volumes:
      - staging_db_data:/var/lib/mysql
    environment:
      MYSQL_ROOT_PASSWORD: staging_password_123
      MYSQL_DATABASE: Access_staging
      MYSQL_USER: staging_user
      MYSQL_PASSWORD: staging_password_456
    networks:
      - staging_network

  backend:
    restart: unless-stopped
    environment:
      - DJANGO_DEBUG=true
      - DJANGO_ALLOWED_HOSTS=localhost,127.0.0.1,staging.company.com
    ports:
      - "8001:8000"  # Different port for staging
    networks:
      - staging_network

  frontend:
    restart: unless-stopped
    environment:
      - NEXT_PUBLIC_API_URL=http://localhost:8001
    ports:
      - "3001:3000"  # Different port for staging
    networks:
      - staging_network

volumes:
  staging_db_data:

networks:
  staging_network:
    driver: bridge
