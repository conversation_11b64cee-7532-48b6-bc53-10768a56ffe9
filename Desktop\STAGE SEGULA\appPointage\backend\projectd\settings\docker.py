# projectd/settings/docker.py
# This file is ONLY used when running in Docker containers
from .base import *
import os

DEBUG = True

ALLOWED_HOSTS = ['localhost', '127.0.0.1', '0.0.0.0', 'backend', 'frontend']

# CORS settings for Docker
CORS_ALLOW_ALL_ORIGINS = True
# CORS_ALLOWED_ORIGINS = [
#     "http://localhost:3000",
#     "http://frontend:3000",
#     "http://127.0.0.1:3000",
# ]
CORS_ALLOW_CREDENTIALS = True

# Get environment variables from docker-compose.yml
SECRET_KEY = os.environ.get('SECRET_KEY', 'django-insecure-fallback-key')

DATABASES = {
    "default": {
        "ENGINE": "django.db.backends.mysql",
        "NAME": os.environ.get('DB_NAME'),  # Access_dev from docker-compose
        "USER": os.environ.get('DB_USER'),  # app_user from docker-compose
        "PASSWORD": os.environ.get('DB_PASSWORD'),  # rourou from docker-compose
        "HOST": os.environ.get('DB_HOST'),  # db from docker-compose
        "PORT": os.environ.get('DB_PORT'),  # 3306 from docker-compose
        "OPTIONS": {
            "init_command": "SET sql_mode='STRICT_TRANS_TABLES'",
            "charset": "utf8mb4",
        },
    }
}

# Email settings for Docker
EMAIL_BACKEND = 'django.core.mail.backends.smtp.EmailBackend'
EMAIL_HOST = 'smtp.gmail.com'
EMAIL_PORT = 587
EMAIL_USE_TLS = True
EMAIL_HOST_USER = os.environ.get('EMAIL_HOST_USER')
EMAIL_HOST_PASSWORD = os.environ.get('EMAIL_HOST_PASSWORD')
DEFAULT_FROM_EMAIL = '<EMAIL>'

FRONTEND_URL = os.environ.get('FRONTEND_URL', 'http://localhost:3000')

# Debug database connection
print("=== DOCKER DATABASE CONFIG ===")
print(f"DB_NAME: {DATABASES['default']['NAME']}")
print(f"DB_USER: {DATABASES['default']['USER']}")
print(f"DB_PASSWORD: {DATABASES['default']['PASSWORD']}")
print(f"DB_HOST: {DATABASES['default']['HOST']}")
print(f"DB_PORT: {DATABASES['default']['PORT']}")
print("===============================")
